provider "aws" {
  region = "us-east-1"
}

locals {
  environment = terraform.workspace
}

# Fetch JSON config from SSM Parameter Store
data "aws_ssm_parameter" "app_config" {
  name           = local.environment
}

locals {
  app_config = jsondecode(data.aws_ssm_parameter.app_config.value)
}

resource "aws_ecs_cluster" "this" {
  name = "${local.environment}-affiliateecs-cluster"
}

resource "aws_iam_role" "ecs_task_execution_role" {
  name = "${local.environment}-ecs-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })
}
# VPC Module
module "vpc" {
  source      = "./modules/vpc"
  environment = local.environment
}

module "alb" {
  source          = "./modules/alb"
  environment     = local.environment
  vpc_id          = module.vpc.vpc_id
  public_subnets  = module.vpc.public_subnets
}

# ECS Module
module "ecs" {
  source              = "./modules/ecs"
  ecr_image           = local.app_config["ECR_IMAGE"]
  environment         = local.environment
  subnets             = module.vpc.private_subnets
  vpc_id              = module.vpc.vpc_id
  s3_bucket_name      = local.app_config["S3_BUCKET_NAME"]
  app_config          = local.app_config
  ecs_task_execution_role = aws_iam_role.ecs_task_execution_role.arn
  cluster             = aws_ecs_cluster.this.id
  target_group_arn    = module.alb.target_group_arn
  alb_security_group_id = module.alb.alb_security_group_id
}

# RDS Module
module "rds" {
  source              = "./modules/rds"
  environment         = local.environment
  vpc_id              = module.vpc.vpc_id
  db_name             = local.app_config["DB_NAME"]
  db_username         = local.app_config["DB_USERNAME"]
  db_password         = local.app_config["DB_PASSWORD"]
  ecs_service_sg_id   = module.ecs.ecs_service_sg_id
  public_subnets      = module.vpc.public_subnets
  private_subnets     = module.vpc.private_subnets
  ip_address          = var.ip_address
}

