/**
 * Middleware to handle Content Manager transaction requests with proper pagination
 */

export default (config, { strapi }) => {
  return async (ctx, next) => {
    // Check if this is a Content Manager transaction request with isFromAdmin
    const isContentManagerTransaction =
      ctx.request.url.includes('/content-manager/collection-types/api::transaction.transaction') &&
      ctx.query.isFromAdmin;

    if (isContentManagerTransaction && ctx.method === 'GET') {
      try {
        console.log('Intercepting Content Manager transaction request');

        // Get all referrals to find users who have referrals
        const referrals = (await strapi.entityService.findMany('api::referral.referral', {
          fields: ['id'],
          populate: {
            user: {
              fields: ['id'],
            },
          },
        })) as any[];

        // Extract user IDs from referrals
        const userIds = referrals
          .filter((referral: any) => referral.user && referral.user.id)
          .map((referral: any) => referral.user.id);

        console.log('Found users with referrals:', userIds.length);

        if (userIds.length > 0) {
          // Parse pagination parameters
          const page = parseInt(ctx.query.page as string) || 1;
          const pageSize = parseInt(ctx.query.pageSize as string) || 10;
          const start = (page - 1) * pageSize;

          // Build filters
          const filters = {
            user: {
              id: {
                $in: userIds,
              },
            },
          };

          // Build populate configuration
          const populate = {
            user: {
              populate: {
                referrer: {
                  fields: ['id', 'referral_code', 'referrer_status'],
                },
                referral: {
                  fields: ['id', 'referral_status', 'total_paid'],
                  populate: {
                    referrer: {
                      fields: ['id', 'referral_code', 'referrer_status'],
                    },
                  },
                },
              },
            },
            subscription_tier: {
              filters: {
                publishedAt: { $ne: null }, // Only get published subscription tiers
              },
            },
          };

          // Get total count with filters
          const totalCount = await strapi.db.query('api::transaction.transaction').count({
            where: filters,
          });

          // Parse sort parameter properly
          let sortConfig: any = { createdAt: 'desc' };
          if (ctx.query.sort) {
            try {
              // Content Manager sends sort as "createdAt:DESC" format
              const sortString = decodeURIComponent(ctx.query.sort as string);
              if (sortString.includes(':')) {
                const [field, direction] = sortString.split(':');
                sortConfig = { [field]: direction.toLowerCase() };
              }
            } catch (error) {
              console.log('Using default sort due to parse error:', error.message);
            }
          }

          // Get paginated results
          const transactions = await strapi.entityService.findMany('api::transaction.transaction', {
            filters: filters,
            populate: populate as any,
            sort: sortConfig,
            start: start,
            limit: pageSize,
          });

          console.log(
            `Middleware - found ${transactions.length} transactions out of ${totalCount} total`
          );

          // Calculate correct pagination metadata
          const pageCount = Math.ceil(totalCount / pageSize);

          // Return response in Content Manager format
          ctx.body = {
            results: transactions,
            pagination: {
              page: page,
              pageSize: pageSize,
              pageCount: pageCount,
              total: totalCount,
            },
          };

          // Skip the normal processing
          return;
        } else {
          // No users with referrals found
          ctx.body = {
            results: [],
            pagination: {
              page: parseInt(ctx.query.page as string) || 1,
              pageSize: parseInt(ctx.query.pageSize as string) || 10,
              pageCount: 0,
              total: 0,
            },
          };
          return;
        }
      } catch (error) {
        console.error('Error in Content Manager transaction middleware:', error);
        // Continue with normal processing on error
      }
    }

    // Continue with normal processing for other requests
    await next();
  };
};
