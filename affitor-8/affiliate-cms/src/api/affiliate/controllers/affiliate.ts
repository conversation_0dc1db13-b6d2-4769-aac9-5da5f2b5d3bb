/**
 * affiliate controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::affiliate.affiliate', ({ strapi }) => ({
  // Keep the original controller functions

  // Override the findOne method to support both ID and slug-based lookups
  async findOne(ctx) {
    const { id } = ctx.params;
    const { query } = ctx;

    let entity;
    // Check if the ID is numeric (standard ID) or a string (slug)
    entity = await strapi.service('api::affiliate.affiliate').findBySlug(id, query.populate);
    if (!entity) {
      // It's a numeric ID, use the default findOne logic
      entity = await strapi.service('api::affiliate.affiliate').findOne(id, query);
    }

    if (!entity) {
      return ctx.notFound('Affiliate not found');
    }

    const sanitizedEntity = await this.sanitizeOutput(entity, ctx);
    return this.transformResponse(sanitizedEntity);
  },

  // Updated method to get affiliate URL using documentId parameter
  async getAffiliateUrl(ctx) {
    const { id } = ctx.params;

    let entity;

    // Check if the ID is numeric (standard ID) or a string (slug)
    entity = await strapi.service('api::affiliate.affiliate').findBySlug(id, {
      populate: ['airtable_data'],
    });
    if (!entity) {
      // It's a numeric ID, use the default findOne logic
      entity = await strapi.service('api::affiliate.affiliate').findOne(id, {
        populate: ['airtable_data'],
      });
    }

    if (!entity) {
      return ctx.notFound('Affiliate not found');
    }

    const userRefRate = entity.airtable_data?.user_ref_rate || 0;

    // Determine which URL to return based on random chance and userRefRate
    let affiliateUrl = entity.url || entity.airtable_data?.user_ref_link;

    // Generate a random number between 0 and 100
    const randomNumber = Math.random();

    console.log({ randomNumber, userRefRate });

    // If random number is less than userRefRate, use referral URL (when available)
    if (randomNumber < userRefRate && entity.airtable_data?.user_ref_link) {
      affiliateUrl = entity.airtable_data.user_ref_link;
    }

    return {
      name: entity.name,
      url: affiliateUrl,
    };
  },

  async getAffiliateSummary(ctx) {
    try {
      const { id } = ctx.params;
      const { user } = ctx.state; // Get the authenticated user from the context

      let entity;

      // Check if the ID is numeric (standard ID) or a string (slug)
      entity = await strapi.service('api::affiliate.affiliate').findBySlug(id, {
        populate: ['payment_methods', 'traffic_webs', 'commission'],
      });
      if (!entity) {
        // It's a numeric ID, use the default findOne logic
        entity = await strapi.service('api::affiliate.affiliate').findOne(id, {
          populate: ['payment_methods', 'traffic_webs', 'commission'],
        });
      }

      if (!entity) {
        return ctx.notFound('Affiliate not found');
      }

      // Get the summary from the service
      const summary = await strapi
        .service('api::affiliate.affiliate')
        .generateAffiliateSummary(entity, user.id);

      return summary;
    } catch (error) {
      ctx.throw(500, error.message);
    }
  },

  // Override the find method to ensure non-null values are always shown first
  async find(ctx) {
    // Get the query parameters
    const { sort, ...restQuery } = ctx.query;

    // If no sorting is needed, use the default find method
    if (!sort) {
      return await super.find(ctx);
    }

    try {
      // Get the results using the original find method
      const { data, meta } = await super.find(ctx);

      // If no data or empty array, return as is
      if (!data || data.length === 0) {
        return { data, meta };
      }

      // Extract field from sort parameter
      let field;
      let isRelationField = false;
      let relationName;
      let actualField;

      // Handle array of sort parameters
      if (Array.isArray(sort) && sort.length > 0) {
        const sortItem = sort[0]; // Take the first sort item

        if (typeof sortItem === 'string') {
          // Handle relation fields in format 'relation.field:direction'
          const [fieldPath, direction = 'asc'] = sortItem.split(':');

          if (fieldPath.includes('.')) {
            isRelationField = true;
            [relationName, actualField] = fieldPath.split('.');
            field = fieldPath; // Store the full path for logging
          } else {
            field = fieldPath;
          }
        } else if (typeof sortItem === 'object' && sortItem !== null && 'field' in sortItem) {
          const fieldPath = sortItem.field as string;

          if (fieldPath.includes('.')) {
            isRelationField = true;
            [relationName, actualField] = fieldPath.split('.');
            field = fieldPath; // Store the full path for logging
          } else {
            field = fieldPath;
          }
        } else {
          // Invalid sort parameter, return original results
          return { data, meta };
        }
      } else if (typeof sort === 'string') {
        // Handle single string sort
        const [fieldPath, direction = 'asc'] = sort.split(':');

        if (fieldPath.includes('.')) {
          isRelationField = true;
          [relationName, actualField] = fieldPath.split('.');
          field = fieldPath; // Store the full path for logging
        } else {
          field = fieldPath;
        }
      } else if (typeof sort === 'object' && sort !== null && 'field' in sort) {
        // Handle single object sort
        const fieldPath = sort.field as string;

        if (fieldPath.includes('.')) {
          isRelationField = true;
          [relationName, actualField] = fieldPath.split('.');
          field = fieldPath; // Store the full path for logging
        } else {
          field = fieldPath;
        }
      } else {
        // Invalid sort parameter, return original results
        return { data, meta };
      }

      console.log('field', field);
      console.log('isRelationField', isRelationField);
      console.log('relationName', relationName);
      console.log('actualField', actualField);

      // Handle relation field sorting
      if (isRelationField && relationName && actualField) {
        // Split based on whether the item has the relation with non-null values
        const withValues = data.filter((item) => {
          // Check if relation exists and has the specified field with a non-null value
          return (
            item[relationName] &&
            item[relationName][actualField] !== null &&
            item[relationName][actualField] !== undefined
          );
        });

        const withoutValues = data.filter((item) => !withValues.includes(item));

        console.log('withValues (relation)', withValues.length);
        console.log('withoutValues (relation)', withoutValues.length);

        // Combine arrays - items with relation values first, then items without
        const sortedData = [...withValues, ...withoutValues];

        // Return the reorganized data with the original metadata
        return {
          data: sortedData,
          meta,
        };
      } else {
        // Handle regular field sorting
        const withValues = data.filter((item) => {
          const value = item[field];
          return value !== null && value !== undefined;
        });

        const withoutValues = data.filter((item) => !withValues.includes(item));

        console.log('withValues', withValues.length);
        console.log('withoutValues', withoutValues.length);

        // Combine arrays - items with values first, then items without values
        const sortedData = [...withValues, ...withoutValues];

        // Return the reorganized data with the original metadata
        return {
          data: sortedData,
          meta,
        };
      }
    } catch (error) {
      console.error('Error in custom find method:', error);
      ctx.throw(500, error.message);
    }
  },

  /**
   * Bulk update recurring_priority for all published affiliates
   * This is an admin-only endpoint for bulk operations
   */
  async bulkUpdateRecurringPriority(ctx) {
    try {
      console.log('Starting bulk update of recurring_priority for published affiliates');

      // Call the service method to perform the bulk update
      const result = await strapi.service('api::affiliate.affiliate').bulkUpdateRecurringPriority();

      // Return the result
      return {
        success: result.success,
        message: result.message,
        data: {
          total: result.total,
          updated: result.updated,
          errors: result.errors,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error in bulk update controller:', error);
      ctx.throw(500, `Bulk update failed: ${error.message}`);
    }
  },
}));
