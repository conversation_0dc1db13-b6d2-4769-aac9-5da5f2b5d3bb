/**
 * referral controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::referral.referral', ({ strapi }) => ({
  async find(ctx) {
    strapi.log.info('Step 1: Starting referral find process');

    const user = ctx.state.user;
    if (!user) {
      strapi.log.info('Step 1 Result: No authenticated user, using default behavior');
      return super.find(ctx);
    }

    strapi.log.info('Step 2: Authenticated user found, ID:', user.id);

    const referrers = await strapi.entityService.findMany('api::referrer.referrer', {
      filters: {
        user: user.id,
      },
    });

    strapi.log.info(
      'Step 3: Referrers search completed, found:',
      referrers?.length || 0,
      'referrers'
    );
    console.log('Referrers found:', referrers);

    const referrer = referrers && referrers.length > 0 ? referrers[0] : null;
    if (!referrer) {
      strapi.log.info('Step 3 Result: No referrer found for user, using default behavior');
      return super.find(ctx);
    }

    strapi.log.info('Step 4: Referrer found, ID:', referrer.id, 'DocumentID:', referrer.documentId);

    // Try both id and documentId for filtering
    const referrerFilter = referrer.documentId
      ? { documentId: referrer.documentId }
      : { id: referrer.id };
    strapi.log.info('Step 4.1: Using referrer filter:', JSON.stringify(referrerFilter));

    ctx.query = {
      ...ctx.query,
      filters: {
        ...(typeof ctx.query.filters === 'object' && ctx.query.filters !== null
          ? ctx.query.filters
          : {}),
        referrer: referrerFilter,
      },
      populate: ['user'],
    };

    strapi.log.info('Step 4.2: Final query structure:', JSON.stringify(ctx.query, null, 2));

    strapi.log.info('Step 5: Executing filtered query for referrals');
    const result = await super.find(ctx);

    strapi.log.info('Step 6: Query executed, found:', result?.data?.length || 0, 'referrals');
    if (result?.data?.length > 0) {
      strapi.log.info('Step 6.1: First referral data:', JSON.stringify(result.data[0], null, 2));
    }

    return result;
  },
}));
