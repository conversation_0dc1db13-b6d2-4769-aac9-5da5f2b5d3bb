{"kind": "collectionType", "collectionName": "aiscripts", "info": {"singularName": "aiscript", "pluralName": "aiscripts", "displayName": "AI Script", "description": "Stores AI script data and execution history"}, "options": {"draftAndPublish": true}, "attributes": {"title": {"type": "string", "required": true}, "content": {"type": "text"}, "model_version": {"type": "string", "description": "The AI model used (e.g., gpt-4o, claude-3-sonnet-3.7, gemini-1.5-pro)"}, "aiscript_session": {"type": "relation", "relation": "manyToOne", "target": "api::aiscript-session.aiscript-session", "inversedBy": "aiscripts"}, "parameters": {"type": "json"}, "input": {"type": "text", "description": "The script input provided by the user"}, "output": {"type": "text", "description": "The AI generated response to the script"}}}