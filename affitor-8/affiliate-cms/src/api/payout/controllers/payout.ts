/**
 * payout controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::payout.payout', ({ strapi }) => ({
  async getOverview(ctx) {
    try {
      // Calculate total earned from referral commissions with status 'ready'
      const totalEarnedResult = await strapi.db
        .query('api::referral-commission.referral-commission')
        .findMany({
          where: {
            commission_status: 'ready',
          },
          select: ['commission_amount'],
        });

      const totalEarned = totalEarnedResult.reduce((sum, commission) => {
        return sum + (parseFloat(commission.commission_amount) || 0);
      }, 0);

      // Calculate total pending payouts
      const pendingPayoutsResult = await strapi.db.query('api::payout.payout').findMany({
        where: {
          payout_status: 'pending',
        },
        select: ['amount'],
      });

      const totalPending = pendingPayoutsResult.reduce((sum, payout) => {
        return sum + (parseFloat(payout.amount) || 0);
      }, 0);

      // Calculate total paid payouts
      const paidPayoutsResult = await strapi.db.query('api::payout.payout').findMany({
        where: {
          payout_status: 'paid',
        },
        select: ['amount'],
      });

      const totalPaid = paidPayoutsResult.reduce((sum, payout) => {
        return sum + (parseFloat(payout.amount) || 0);
      }, 0);

      return ctx.send({
        data: {
          total_earned: totalEarned,
          pending: totalPending,
          paid: totalPaid,
        },
      });
    } catch (error) {
      console.error('Error fetching payout overview:', error);
      return ctx.internalServerError('Failed to fetch payout overview');
    }
  },
}));
