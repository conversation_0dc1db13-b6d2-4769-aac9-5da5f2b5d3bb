export default {
  routes: [
    {
      method: 'POST',
      path: '/referrers/register',
      handler: 'referrer.register',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/admin/affiliate-program/dashboard',
      handler: 'referrer.dashboard',
      config: {
        policies: [], // You can add custom policies if needed
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/referrers/sync-data',
      handler: 'referrer.syncData',
      config: {
        policies: [],
        middlewares: [],
        description: 'Sync referrer financial data - supports single referrer or bulk operation',
      },
    },
  ],
};
