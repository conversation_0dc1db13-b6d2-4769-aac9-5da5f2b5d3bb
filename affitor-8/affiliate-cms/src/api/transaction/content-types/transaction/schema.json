{"kind": "collectionType", "collectionName": "transactions", "info": {"singularName": "transaction", "pluralName": "transactions", "displayName": "Transaction", "description": "Record of subscription purchases"}, "options": {"draftAndPublish": false}, "attributes": {"user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "subscription_tier": {"type": "relation", "relation": "manyToOne", "target": "api::subscription-tier.subscription-tier", "inversedBy": "transactions"}, "amount": {"type": "decimal", "required": true, "min": 0}, "currency": {"type": "enumeration", "enum": ["USD", "EUR"], "default": "USD", "required": true}, "payment_status": {"type": "enumeration", "enum": ["pending", "completed", "failed", "refunded", "cancelled", "past_due", "unpaid"], "default": "pending", "required": true}, "payment_method": {"type": "enumeration", "enum": ["stripe", "free"]}, "transaction_date": {"type": "datetime", "required": true}, "payment_details": {"type": "json"}, "stripe_checkout_session": {"type": "string", "description": "Stripe checkout session ID"}, "stripe_subscription_id": {"type": "string", "description": "Stripe subscription ID"}, "stripe_customer_id": {"type": "string", "description": "Stripe customer ID"}, "stripe_price_id": {"type": "string", "description": "Stripe price ID used for this subscription"}, "stripe_invoice_id": {"type": "string", "description": "Latest invoice ID for this subscription"}, "current_period_start": {"type": "datetime", "description": "Start date of current billing period"}, "current_period_end": {"type": "datetime", "description": "End date of current billing period"}, "auto_renew": {"type": "boolean", "default": true, "description": "Indicates if the subscription will renew automatically"}, "cancellation_date": {"type": "datetime", "description": "Date when subscription was cancelled"}, "cancellation_reason": {"type": "string"}, "child_transactions": {"type": "relation", "relation": "oneToMany", "target": "api::transaction.transaction", "mappedBy": "parent_transaction"}, "parent_transaction": {"type": "relation", "relation": "manyToOne", "target": "api::transaction.transaction", "inversedBy": "child_transactions"}, "is_checked_subscription": {"type": "boolean", "default": false}}}