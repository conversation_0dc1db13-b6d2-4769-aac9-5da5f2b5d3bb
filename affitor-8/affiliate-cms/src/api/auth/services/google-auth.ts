import axios from 'axios';
import querystring from 'querystring';

interface GoogleTokenResponse {
  access_token: string;
  expires_in: number;
  refresh_token?: string;
  scope: string;
  token_type: string;
  id_token: string;
}

interface GoogleUserInfo {
  id: string;
  email: string;
  verified_email: boolean;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
  locale: string;
}

export default ({ strapi }) => {
  // Create the service object
  const service = {
    getAuthUrl: () => {
      const rootUrl = 'https://accounts.google.com/o/oauth2/v2/auth';
      const options = {
        // Make sure the redirect URI exactly matches what's configured in Google Cloud Console
        redirect_uri: process.env.GOOGLE_CALLBACK_URL,
        client_id: process.env.GOOGLE_CLIENT_ID,
        access_type: 'offline',
        response_type: 'code',
        prompt: 'consent',
        scope: [
          'https://www.googleapis.com/auth/userinfo.profile',
          'https://www.googleapis.com/auth/userinfo.email',
        ].join(' '),
      };

      console.log('Generated auth URL options:', options);
      return `${rootUrl}?${querystring.stringify(options)}`;
    },

    getTokens: async (code: string): Promise<GoogleTokenResponse> => {
      const url = 'https://oauth2.googleapis.com/token';
      console.log('LOG-process.env.GOOGLE_CLIENT_SECRET', process.env.GOOGLE_CLIENT_SECRET);
      const values = {
        code,
        client_id: process.env.GOOGLE_CLIENT_ID,
        client_secret: process.env.GOOGLE_CLIENT_SECRET?.startsWith('GOCSPX-')
          ? process.env.GOOGLE_CLIENT_SECRET
          : `GOCSPX-${process.env.GOOGLE_CLIENT_SECRET}`,
        // Make sure the redirect URI exactly matches what's configured in Google Cloud Console
        redirect_uri: process.env.GOOGLE_CALLBACK_URL,
        grant_type: 'authorization_code',
      };

      console.log('Sending token request with values:', {
        ...values,
        client_secret: process.env.GOOGLE_CLIENT_SECRET,
      });

      try {
        const response = await axios.post<GoogleTokenResponse>(url, querystring.stringify(values), {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        });
        console.log('Token response received:', {
          status: response.status,
          headers: response.headers,
        });
        return response.data;
      } catch (error) {
        console.error(
          'Failed to fetch Google tokens:',
          error.response
            ? {
                status: error.response.status,
                data: error.response.data,
                headers: error.response.headers,
              }
            : error.message
        );
        throw new Error(
          `Failed to fetch Google tokens: ${error.response?.data?.error_description || error.message}`
        );
      }
    },

    getUserInfo: async (accessToken: string): Promise<GoogleUserInfo> => {
      try {
        const response = await axios.get<GoogleUserInfo>(
          'https://www.googleapis.com/oauth2/v2/userinfo',
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );
        return response.data;
      } catch (error) {
        console.error('Failed to fetch Google user info:', error);
        throw new Error('Failed to fetch Google user info');
      }
    },

    authenticate: async (code: string) => {
      // Get Google tokens using the service method reference, not this
      const tokens = await service.getTokens(code);

      // Get user info from Google using the service method reference, not this
      const googleUser = await service.getUserInfo(tokens.access_token);

      // Find or create a user with this email
      const { email, name } = googleUser;

      // Check if user exists
      const users = await strapi.entityService.findMany('plugin::users-permissions.user', {
        filters: { email },
      });

      let user;
      let role;

      if (!users || users.length === 0) {
        // Get the default role
        const pluginStore = await strapi.store({
          type: 'plugin',
          name: 'users-permissions',
        });

        const settings = await pluginStore.get({ key: 'advanced' });

        if (settings.default_role) {
          role = await strapi
            .query('plugin::users-permissions.role')
            .findOne({ where: { type: settings.default_role } });
        } else {
          role = await strapi
            .query('plugin::users-permissions.role')
            .findOne({ where: { type: 'authenticated' } });
        }

        // Create new user
        user = await strapi.entityService.create('plugin::users-permissions.user', {
          data: {
            username: email.split('@')[0],
            email,
            provider: 'google',
            confirmed: true,
            role: role.id,
            displayName: name,
          },
        });
      } else {
        user = users[0];
      }

      // Generate JWT token
      const jwt = strapi.plugins['users-permissions'].services.jwt.issue({
        id: user.id,
      });

      return {
        jwt,
        user,
      };
    },
  };

  return service;
};
