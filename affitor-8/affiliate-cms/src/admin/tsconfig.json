{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["../plugins/**/admin/src/**/*", "./"], "exclude": ["node_modules/", "build/", "dist/", "**/*.test.ts"]}