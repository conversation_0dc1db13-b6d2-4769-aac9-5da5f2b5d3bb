import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import { 
  FileText, 
  Plus, 
  Edit, 
  Trash2, 
  Copy, 
  Check, 
  Loader, 
  AlertCircle,
  ArrowLeft
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { actions as pageActions } from '@/features/page/page.slice';
import {
  selectPageData,
  selectPageLoading,
  selectPageError,
  selectCreatePageLoading,
  selectDeletePageLoading,
  selectDeletePageError,
  selectPageSuccessMessage,
} from '@/features/page/page.slice';
import { useToast } from '@/context/ToastContext';

const PagesListPage = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { showToast } = useToast();
  
  const [copiedPageId, setCopiedPageId] = useState<string | null>(null);
  const [pageToDelete, setPageToDelete] = useState<any>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const userPages = useSelector(selectPageData);
  const pagesLoading = useSelector(selectPageLoading);
  const pagesError = useSelector(selectPageError);
  const createPageLoading = useSelector(selectCreatePageLoading);
  const deletePageLoading = useSelector(selectDeletePageLoading);
  const deletePageError = useSelector(selectDeletePageError);
  const successMessage = useSelector(selectPageSuccessMessage);

  useEffect(() => {
	// Fetch all user pages
	dispatch(pageActions.fetchPagesRequest({ page: 1, pageSize: 50 }));
  }, [dispatch]);

  // Handle success/error messages
  useEffect(() => {
	if (successMessage) {
	  showToast('Success', successMessage, 'success');
	  dispatch(pageActions.clearSuccessMessage());
	}
  }, [successMessage, showToast, dispatch]);

  useEffect(() => {
	if (deletePageError) {
	  showToast('Error', deletePageError, 'destructive');
	  dispatch(pageActions.clearErrors());
	}
  }, [deletePageError, showToast, dispatch]);

  // Refresh pages after operations
  useEffect(() => {
	if (!createPageLoading || !deletePageLoading) {
	  const timer = setTimeout(() => {
		dispatch(pageActions.fetchPagesRequest({ page: 1, pageSize: 50 }));
	  }, 1000);
	  return () => clearTimeout(timer);
	}
  }, [createPageLoading, deletePageLoading, dispatch]);

  const handleCreatePage = () => {
	const newPageData = {
	  title: 'Untitled Page',
	  content: {
		'block-1': {
		  id: 'block-1',
		  type: 'paragraph' as const,
		  value: [{ text: 'Start writing your content...' }],
		  meta: { order: 0, depth: 0 }
		}
	  },
	  excerpt: '',
	  status: 'draft' as const,
	};
	dispatch(pageActions.createPageRequest(newPageData));
  };

  const handleEditPage = (pageId: string) => {
	router.push(`/editor/${pageId}`);
  };

  const handleCopyLink = async (pageId: string) => {
	try {
	  const url = `${window.location.origin}/editor/${pageId}`;
	  await navigator.clipboard.writeText(url);
	  setCopiedPageId(pageId);
	  setTimeout(() => setCopiedPageId(null), 2000);
	} catch (error) {
	  console.error('Failed to copy link:', error);
	  showToast('Failed to copy link', 'error');
	}
  };

  const handleDeletePage = (page: any) => {
	setPageToDelete(page);
	setIsDeleteDialogOpen(true);
  };

  const confirmDeletePage = () => {
	if (pageToDelete) {
	  dispatch(pageActions.deletePageRequest(pageToDelete.documentId));
	  setIsDeleteDialogOpen(false);
	  setPageToDelete(null);
	}
  };

  const cancelDeletePage = () => {
	setIsDeleteDialogOpen(false);
	setPageToDelete(null);
  };

  return (
	<>
	  <Head>
		<title>My Pages - Affitor Dashboard</title>
		<meta name="description" content="Manage all your pages" />
	  </Head>
	  
	  <div className="max-w-6xl mx-auto p-6">
		{/* Header */}
		<div className="flex items-center justify-between mb-6">
		  <div className="flex items-center gap-4">
			<Button
			  variant="ghost"
			  size="sm"
			  onClick={() => router.push('/profile')}
			  className="flex items-center gap-2"
			>
			  <ArrowLeft className="w-4 h-4" />
			  Back to Profile
			</Button>
			<div>
			  <h1 className="text-2xl font-bold dark:text-white">My Pages</h1>
			  <p className="text-gray-600 dark:text-gray-400">
				Manage all your pages in one place
			  </p>
			</div>
		  </div>
		  <Button
			onClick={handleCreatePage}
			disabled={createPageLoading}
			className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
		  >
			{createPageLoading ? (
			  <Loader className="w-4 h-4 animate-spin" />
			) : (
			  <Plus className="w-4 h-4" />
			)}
			Create New Page
		  </Button>
		</div>

		{/* Pages Grid */}
		<div className="bg-white dark:bg-gray-800 rounded-lg shadow">
		  {pagesLoading ? (
			<div className="flex items-center justify-center py-12">
			  <Loader className="w-8 h-8 animate-spin text-blue-500" />
			  <span className="ml-3 text-gray-600 dark:text-gray-400">
				Loading your pages...
			  </span>
			</div>
		  ) : pagesError ? (
			<div className="text-center py-12">
			  <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
			  <p className="text-red-600 dark:text-red-400 mb-4">
				Failed to load your pages: {pagesError}
			  </p>
			  <Button
				onClick={() => dispatch(pageActions.fetchPagesRequest({ page: 1, pageSize: 50 }))}
				variant="outline"
			  >
				Try Again
			  </Button>
			</div>
		  ) : userPages.length > 0 ? (
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6">
			  {userPages.map((page) => (
				<div
				  key={page.documentId}
				  className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
				>
				  <div className="flex flex-col h-full">
					<div className="flex-1 mb-3">
					  <Tooltip>
						<TooltipTrigger asChild>
						  <h3 
							className="font-medium text-gray-900 dark:text-white truncate max-w-full cursor-default mb-2" 
							title={page.title}
						  >
							{page.title}
						  </h3>
						</TooltipTrigger>
						<TooltipContent side="top" className="max-w-xs">
						  <p className="break-words">{page.title}</p>
						</TooltipContent>
					  </Tooltip>
					  
					  <div className="flex items-center gap-2 mb-2">
						<span className={`px-2 py-1 text-xs rounded-full ${
						  page.status === 'published'
							? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
							: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
						}`}>
						  {page.status}
						</span>
						<span className="text-xs text-gray-500 dark:text-gray-400">
						  {new Date(page.updatedAt).toLocaleDateString()}
						</span>
					  </div>
					  
					  {page.excerpt && (
						<p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
						  {page.excerpt}
						</p>
					  )}
					</div>
					
					<div className="flex items-center justify-between pt-3 border-t border-gray-100 dark:border-gray-700">
					  <div className="flex items-center gap-1">
						<Button
						  variant="ghost"
						  size="sm"
						  onClick={() => handleCopyLink(page.documentId)}
						  title="Copy Link"
						  className="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
						>
						  {copiedPageId === page.documentId ? (
							<Check className="w-4 h-4 text-green-600" />
						  ) : (
							<Copy className="w-4 h-4" />
						  )}
						</Button>
						<Button
						  variant="ghost"
						  size="sm"
						  onClick={() => handleEditPage(page.documentId)}
						  title="Edit Page"
						>
						  <Edit className="w-4 h-4" />
						</Button>
						<Button
						  variant="ghost"
						  size="sm"
						  onClick={() => handleDeletePage(page)}
						  title="Delete Page"
						  disabled={deletePageLoading}
						  className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20 disabled:opacity-50"
						>
						  {deletePageLoading && pageToDelete?.documentId === page.documentId ? (
							<Loader className="w-4 h-4 animate-spin" />
						  ) : (
							<Trash2 className="w-4 h-4" />
						  )}
						</Button>
					  </div>
					</div>
				  </div>
				</div>
			  ))}
			</div>
		  ) : (
			<div className="text-center py-12">
			  <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
			  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
				No pages yet
			  </h3>
			  <p className="text-gray-500 dark:text-gray-400 mb-6">
				Get started by creating your first page
			  </p>
			  <Button
				onClick={handleCreatePage}
				disabled={createPageLoading}
				className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
			  >
				{createPageLoading ? (
				  <Loader className="w-4 h-4 animate-spin" />
				) : (
				  <Plus className="w-4 h-4" />
				)}
				Create Your First Page
			  </Button>
			</div>
		  )}
		</div>
	  </div>
	</>
  );
};

export default PagesListPage;
