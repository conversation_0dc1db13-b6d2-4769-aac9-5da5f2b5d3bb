import React, { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';
import Head from 'next/head';
import { Button } from '@/components/ui/button';
import { ModeToggle } from '@/components/mode-toggle';
import YooptaRichTextEditor from '@/components/YooptaRichTextEditor';
import { actions } from '@/features/page/page.slice';
import { actions as userActions, selectUserData } from '@/features/user/user.slice';
import type { YooptaContentValue } from '@/types/yoopta-editor';
import {
  selectCurrentPage,
  selectPageLoading,
  selectUpdatePageLoading,
  selectAutoSaveLoading,
  selectPublishLoading,
  selectPageError,
  selectHasUnsavedChanges,
  selectLastAutoSave,
} from '@/features/page/page.slice';
import {
  ArrowLeft,
  Save,
  Globe,
  Loader,
  AlertCircle,
  CheckCircle,
} from 'lucide-react';

const PageEditor: React.FC = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { id } = router.query;

  // Check if user is authenticated (for feature access control)
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  console.log("📝 [Page Editor] Component initialized:", {
    id,
    idType: typeof id,
    routerQuery: router.query,
    routerReady: router.isReady,
    routerAsPath: router.asPath,
    routerPathname: router.pathname,
  });

  // Check authentication status and fetch user data
  useEffect(() => {
    if (typeof window !== "undefined") {
      const token = localStorage.getItem("auth_token");
      setIsAuthenticated(!!token);

      console.log("📝 [Page Editor] Auth check:", {
        hasToken: !!token,
        isAuthenticated: !!token,
      });

      // Fetch user data if authenticated
      if (token) {
        dispatch(userActions.fetchUserMe());
      }
    }
  }, [dispatch]);

  // Redux state
  const currentPage = useSelector(selectCurrentPage);
  const loading = useSelector(selectPageLoading);
  const updateLoading = useSelector(selectUpdatePageLoading);
  const autoSaveLoading = useSelector(selectAutoSaveLoading);
  const publishLoading = useSelector(selectPublishLoading);
  const error = useSelector(selectPageError);
  const hasUnsavedChanges = useSelector(selectHasUnsavedChanges);
  const lastAutoSave = useSelector(selectLastAutoSave);
  const currentUser = useSelector(selectUserData);
  const [isPageOwner, setIsPageOwner] = useState(false);

  // Effect to check and update page ownership
  useEffect(() => {
    console.log('🔍 [Page Editor] Checking page ownership:', {
      hasCurrentUser: !!currentUser,
      currentUserId: currentUser?.id,
      hasCurrentPage: !!currentPage,
      updateLoading,
      publishLoading,
      currentPageStructure: currentPage ? {
        hasDirectAuthor: !!currentPage.author,
        hasNestedAuthor: !!(currentPage as any).data?.author,
        directAuthorId: currentPage.author?.id,
        nestedAuthorId: (currentPage as any).data?.author?.id,
        pageKeys: Object.keys(currentPage),
        // Log key properties to compare before/after save
        pageComparison: {
          id: currentPage.id,
          documentId: currentPage.documentId,
          title: currentPage.title,
          status: currentPage.status,
          author: currentPage.author,
          dataProperty: (currentPage as any).data,
          fullStructure: JSON.stringify(currentPage, null, 2)
        }
      } : null
    });

    if (!currentUser || !currentPage) {
      const newState = false;
      if (newState !== isPageOwner) {
        console.log('🔍 [Page Editor] Setting ownership to false (no user or page)');
        setIsPageOwner(newState);
      }
      return;
    }

    // Handle both direct author and nested Strapi response structure
    const author = currentPage.author || (currentPage as any).data?.author;
    const result = author && currentUser.id === author.id;

    console.log('🔍 [Page Editor] Ownership result:', {
      authorFound: !!author,
      authorId: author?.id,
      authorStructure: author,
      currentUserId: currentUser.id,
      isOwner: result,
      previousOwnerState: isPageOwner,
      loadingStates: { updateLoading, publishLoading }
    });

    const newOwnershipState = !!result;
    if (newOwnershipState !== isPageOwner) {
      console.log('🔍 [Page Editor] 🚨 OWNERSHIP STATE CHANGING:', {
        from: isPageOwner,
        to: newOwnershipState,
        reason: !currentUser ? 'no_user' : !currentPage ? 'no_page' : !author ? 'no_author' : 'ownership_check',
        pageDataChanged: 'Check page structure above for differences',
        stackTrace: new Error().stack
      });
    }

    setIsPageOwner(newOwnershipState);
  }, [currentUser, currentPage, isPageOwner, updateLoading, publishLoading]);

  // Local state
  const [title, setTitle] = useState("");
  const [content, setContent] = useState<YooptaContentValue | undefined>(
    undefined
  );
  const [titleFocused, setTitleFocused] = useState(false);
  const [isPageDataReady, setIsPageDataReady] = useState(false);

  // Ref to track when we're syncing from Redux to prevent triggering change handlers
  const isSyncingFromRedux = useRef(false);

  // Load page data
  useEffect(() => {
    console.log("📝 [Page Editor] Router query changed:", {
      id,
      idType: typeof id,
      isString: typeof id === "string",
      query: router.query,
      isReady: router.isReady,
    });

    // Only fetch when router is ready and we have a valid ID
    if (router.isReady && id && typeof id === "string") {
      console.log("📝 [Page Editor] Dispatching fetchPageRequest for ID:", id);
      dispatch(actions.fetchPageRequest(id));
    } else if (router.isReady && !id) {
      console.log("📝 [Page Editor] Router ready but no page ID found");
    } else if (!router.isReady) {
      console.log("📝 [Page Editor] Router not ready yet, waiting...");
    } else {
      console.log("📝 [Page Editor] Invalid or missing page ID:", {
        id,
        type: typeof id,
      });
    }
  }, [id, dispatch, router.query, router.isReady]);

  // Update local state when page data changes
  useEffect(() => {
    const timestamp = new Date().toISOString();
    console.log(
      `🔍 [Page Editor] [${timestamp}] REDUX STATE EFFECT - Redux state changed:`,
      {
        hasCurrentPage: !!currentPage,
        loading,
        error,
        updateLoading,
        publishLoading,
        hasUnsavedChanges,
        isPageDataReady,
        pageData: currentPage
          ? {
              id: currentPage.id,
              documentId: currentPage.documentId,
              title: currentPage.title,
              status: currentPage.status,
              hasContent: !!currentPage.content,
              contentType: typeof currentPage.content,
              contentValue: currentPage.content,
              contentKeys: currentPage.content
                ? Object.keys(currentPage.content)
                : [],
            }
          : null,
      }
    );

    console.log(`🔍 [Page Editor] [${timestamp}] STATUS: Status check:`, {
      currentPageStatus: currentPage?.status,
      isPublished: currentPage?.status === "published",
      isDraft: currentPage?.status === "draft",
    });

    // Reset page data ready state when loading starts
    if (loading) {
      console.log(
        `🔍 [Page Editor] [${timestamp}] LOADING: Resetting page data ready state`
      );
      setIsPageDataReady(false);
    }

    if (currentPage && !loading) {
      console.log(
        `🔍 [Page Editor] [${timestamp}] PROCESSING: Updating local state with page data`
      );
      console.log(`🔍 [Page Editor] [${timestamp}] CONTENT ANALYSIS:`, {
        content: currentPage.content,
        isNull: currentPage.content === null,
        isUndefined: currentPage.content === undefined,
        isEmpty:
          currentPage.content && Object.keys(currentPage.content).length === 0,
        type: typeof currentPage.content,
        keys: currentPage.content ? Object.keys(currentPage.content) : "N/A",
      });

      // Always update local state to match Redux state
      // This ensures local state is synchronized after save operations
      console.log(
        `🔍 [Page Editor] [${timestamp}] SYNC: Updating local state from Redux:`,
        {
          reduxTitle: currentPage.title,
          localTitle: title,
          reduxContent: currentPage.content,
          hasUnsavedChanges,
          source: "redux_sync",
        }
      );

      // Set flag to prevent change handlers from dispatching Redux actions
      isSyncingFromRedux.current = true;

      setTitle(currentPage.title || "");

      // Handle content more carefully
      if (currentPage.content === null || currentPage.content === undefined) {
        console.log(
          `🔍 [Page Editor] [${timestamp}] CONTENT: Content is null/undefined, setting to undefined`
        );
        setContent(undefined);
      } else if (
        typeof currentPage.content === "object" &&
        Object.keys(currentPage.content).length === 0
      ) {
        console.log(
          `🔍 [Page Editor] [${timestamp}] CONTENT: Content is empty object, setting to undefined`
        );
        setContent(undefined);
      } else {
        console.log(
          `🔍 [Page Editor] [${timestamp}] CONTENT: Setting content:`,
          currentPage.content
        );
        setContent(currentPage.content);
      }

      // Reset flag after a brief delay to allow state updates to complete
      setTimeout(() => {
        isSyncingFromRedux.current = false;
        console.log(
          `🔍 [Page Editor] [${timestamp}] SYNC: Completed Redux sync, flag reset`
        );
      }, 0);

      // Mark page data as ready only after we've processed the content
      console.log(
        `🔍 [Page Editor] [${timestamp}] READY: Page data is now ready for editor`
      );
      setIsPageDataReady(true);
    } else if (!loading && error) {
      console.log(
        `🔍 [Page Editor] [${timestamp}] ERROR: No current page and error present:`,
        error
      );
      setIsPageDataReady(false);
    } else if (!loading && !currentPage) {
      console.log(
        `🔍 [Page Editor] [${timestamp}] NOT FOUND: No current page and no loading - page not found`
      );
      setIsPageDataReady(false);
    } else {
      console.log(
        `🔍 [Page Editor] [${timestamp}] WAITING: Still loading or processing...`
      );
    }
  }, [
    currentPage,
    loading,
    error,
    updateLoading,
    publishLoading,
    hasUnsavedChanges,
  ]);

  // Handle content changes with validation
  const handleContentChange = (newContent: YooptaContentValue) => {
    try {
      console.log("📝 [Page Editor] Content change received:", {
        hasContent: !!newContent,
        contentType: typeof newContent,
        blockCount: newContent ? Object.keys(newContent).length : 0,
        currentHasUnsavedChanges: hasUnsavedChanges,
        isSyncingFromRedux: isSyncingFromRedux.current,
        source: isSyncingFromRedux.current ? "redux_sync" : "user_input",
      });

      setContent(newContent);

      // Only dispatch Redux action if this is not a sync from Redux
      if (!isSyncingFromRedux.current) {
        dispatch(actions.updateCurrentPageContent(newContent));
      }
    } catch (error) {
      console.error("📝 [Page Editor] Error handling content change:", error);
      // Don't update if there's an error
    }
  };

  // Handle title changes
  const handleTitleChange = (newTitle: string) => {
    console.log("📝 [Page Editor] Title change received:", {
      newTitle,
      currentHasUnsavedChanges: hasUnsavedChanges,
      isSyncingFromRedux: isSyncingFromRedux.current,
      source: isSyncingFromRedux.current ? "redux_sync" : "user_input",
    });
    setTitle(newTitle);

    // Only dispatch Redux action if this is not a sync from Redux
    if (!isSyncingFromRedux.current) {
      dispatch(actions.updateCurrentPageTitle(newTitle));
    }
  };

  // Handle auto-save
  const handleAutoSave = (newContent: any) => {
    if (!isAuthenticated) {
      console.log(
        "📝 [Page Editor] Skipping auto-save - user not authenticated"
      );
      return;
    }

    if (!isPageOwner) {
      console.log(
        "📝 [Page Editor] Skipping auto-save - user is not page owner"
      );
      return;
    }

    if (id && typeof id === "string") {
      dispatch(actions.autoSavePageRequest({ id, content: newContent }));
    }
  };

  // Keyboard shortcuts with loading state awareness
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Cmd/Ctrl + S to save (only if not already loading and has unsaved changes)
      if ((event.metaKey || event.ctrlKey) && event.key === "s") {
        event.preventDefault();
        if (
          hasUnsavedChanges &&
          !updateLoading &&
          !publishLoading &&
          id &&
          typeof id === "string"
        ) {
          handleSave();
        }
      }

      // Cmd/Ctrl + Shift + P to publish (only if not already loading and not published)
      if (
        (event.metaKey || event.ctrlKey) &&
        event.shiftKey &&
        event.key === "P"
      ) {
        event.preventDefault();
        if (
          currentPage &&
          currentPage.status !== "published" &&
          !updateLoading &&
          !publishLoading &&
          id &&
          typeof id === "string"
        ) {
          handlePublish();
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [
    hasUnsavedChanges,
    currentPage,
    updateLoading,
    publishLoading,
    id,
    title,
    content,
    dispatch,
  ]);

  // Handle manual save
  const handleSave = () => {
    if (!isAuthenticated) {
      alert("You must be logged in to save pages. Please log in to continue.");
      return;
    }

    if (!isPageOwner) {
      alert("You do not have permission to edit this page.");
      return;
    }

    if (id && typeof id === "string") {
      dispatch(
        actions.updatePageRequest({
          id,
          data: {
            title,
            content,
          },
        })
      );
    }
  };

  // Handle publish with auto-save
  const handlePublish = () => {
    if (!isAuthenticated) {
      alert(
        "You must be logged in to publish pages. Please log in to continue."
      );
      return;
    }

    if (!isPageOwner) {
      alert("You do not have permission to publish this page.");
      return;
    }

    if (id && typeof id === "string") {
      console.log("📝 [Page Editor] Publishing page:", {
        pageId: id,
        currentStatus: currentPage?.status,
        hasUnsavedChanges,
      });

      // Auto-save before publish if there are unsaved changes
      if (hasUnsavedChanges) {
        console.log("📝 [Page Editor] Auto-saving before publish...");
        // First save the page, then publish will be handled by the saga
        dispatch(
          actions.updatePageRequest({
            id,
            data: {
              title,
              content,
            },
            shouldPublishAfterSave: true, // Flag to indicate publish should follow
          })
        );
      } else {
        // No unsaved changes, proceed directly with publish
        dispatch(actions.publishPageRequest(id));
      }
    }
  };

  // Handle back navigation with smart unsaved changes detection
  const handleBack = () => {
    if (hasUnsavedChanges && isPageOwner) {
      const confirmLeave = window.confirm(
        "You have unsaved changes. Are you sure you want to leave?"
      );
      if (!confirmLeave) return;
    }

    // Navigate to homepage for non-owners, profile for owners
    if (isPageOwner) {
      router.push("/profile");
    } else {
      router.push("/");
    }
  };

  // Handle browser navigation (beforeunload) with smart unsaved changes detection
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        // Modern approach: just prevent default, browsers show their own message
        event.preventDefault();
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [hasUnsavedChanges]);

  if (!router.isReady) {
    console.log("📝 [Page Editor] Rendering router loading state", {
      routerReady: router.isReady,
    });
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader className="h-5 w-5 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    );
  }

  if (loading && !currentPage) {
    console.log("📝 [Page Editor] Rendering page loading state", {
      loading,
      hasCurrentPage: !!currentPage,
    });
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader className="h-5 w-5 animate-spin" />
          <span>Loading page...</span>
        </div>
      </div>
    );
  }

  if (error) {
    console.log("📝 [Page Editor] Rendering error state:", error);
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-xl font-semibold mb-2">Error Loading Page</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={() => router.push("/profile")}>
            Go Back to Profile
          </Button>
        </div>
      </div>
    );
  }

  if (!currentPage && router.isReady && !loading) {
    console.log("📝 [Page Editor] Rendering page not found state:", {
      hasCurrentPage: !!currentPage,
      loading,
      error,
      pageId: id,
      routerReady: router.isReady,
    });
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-xl font-semibold mb-2">Page Not Found</h1>
          <p className="text-gray-600 mb-4">
            The page you're looking for doesn't exist.
          </p>
          <Button onClick={() => router.push("/profile")}>
            Go Back to Profile
          </Button>
        </div>
      </div>
    );
  }

  console.log("📝 [Page Editor] Rendering page editor with data:", {
    pageId: currentPage?.id,
    documentId: currentPage?.documentId,
    title: currentPage?.title,
  });

  return (
    <>
      <Head>
        <title>{title ? `${title}` : "Untitled"} | Affiliate Dashboard</title>
        <meta
          name="description"
          content="Edit your page content with our rich text editor"
        />
        <style jsx>{`
          .notion-editor {
            font-family: ui-sans-serif, system-ui, -apple-system,
              BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial,
              "Noto Sans", sans-serif;
            width: 100% !important;
            max-width: 100% !important;
          }

          .notion-editor .yoopta-editor {
            border: none !important;
            box-shadow: none !important;
            background: transparent !important;
            padding: 0 !important;
            width: 100% !important;
            max-width: 100% !important;
          }

          .notion-editor .yoopta-block {
            margin: 0.5rem 0 !important;
            padding: 0.25rem 0 !important;
            width: 100% !important;
            max-width: 100% !important;
            box-sizing: border-box !important;
          }

          .notion-editor .yoopta-block:hover {
            background: rgba(0, 0, 0, 0.02) !important;
            border-radius: 4px !important;
          }

          .notion-editor .yoopta-block-selected {
            background: rgba(59, 130, 246, 0.1) !important;
            border-radius: 4px !important;
          }

          .notion-editor p {
            font-size: 16px !important;
            line-height: 1.6 !important;
            color: rgb(55, 53, 47) !important;
            margin: 0 !important;
          }

          .notion-editor h1 {
            font-size: 2rem !important;
            font-weight: 700 !important;
            line-height: 1.2 !important;
            margin: 1rem 0 0.5rem 0 !important;
          }

          .notion-editor h2 {
            font-size: 1.5rem !important;
            font-weight: 600 !important;
            line-height: 1.3 !important;
            margin: 0.75rem 0 0.5rem 0 !important;
          }

          .notion-editor h3 {
            font-size: 1.25rem !important;
            font-weight: 600 !important;
            line-height: 1.4 !important;
            margin: 0.5rem 0 0.25rem 0 !important;
          }

          .notion-editor .yoopta-placeholder {
            color: rgb(156, 163, 175) !important;
            font-style: normal !important;
          }

          @media (prefers-color-scheme: dark) {
            .notion-editor .yoopta-block:hover {
              background: rgba(255, 255, 255, 0.05) !important;
            }

            .notion-editor .yoopta-placeholder {
              color: rgb(107, 114, 128) !important;
            }
          }

          .notion-content-area {
            width: 100% !important;
            max-width: 100% !important;
          }

          .notion-style-editor {
            width: 100% !important;
            max-width: 100% !important;
          }
        `}</style>
      </Head>

      <div className="min-h-screen bg-white dark:bg-gray-900">
        {/* Notion-style Header */}
        <header className="sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800">
          <div className="md:max-w-[80%] mx-auto px-6">
            <div className="flex items-center justify-between h-14">
              {/* Left side - minimal */}
              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBack}
                  className="flex items-center gap-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  {isPageOwner ? (
                    <>
                      <ArrowLeft className="h-4 w-4" />
                      <span className="hidden sm:inline">Back</span>
                    </>
                  ) : (
                    <span className="text-[28px] font-bold text-[#3861FB] cursor-pointer">Affitor</span>
                  )}
                </Button>
              </div>

              {/* Right side - contextual actions */}
              <div className="flex items-center gap-3">
                {/* View-only indicator for non-owners */}
                {!isPageOwner && currentPage && (
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span>View Only</span>
                  </div>
                )}

                {/* Status indicator */}
                {currentPage?.status === "draft" && (
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                    <span>Draft</span>
                  </div>
                )}
                {currentPage?.status === "published" && (
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span>Published</span>
                  </div>
                )}

                {/* Auto-save indicator */}
                {lastAutoSave && !hasUnsavedChanges && (
                  <div className="hidden sm:flex items-center gap-1 text-xs text-gray-400">
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    <span>Saved</span>
                  </div>
                )}

                {autoSaveLoading && (
                  <div className="flex items-center gap-1 text-xs text-gray-400">
                    <Loader className="h-3 w-3 animate-spin" />
                    <span className="hidden sm:inline">Saving</span>
                  </div>
                )}

                {/* Dark mode toggle - always visible */}
                <ModeToggle />

                {/* Action buttons for page owners only */}
                {isAuthenticated && isPageOwner && (
                  <div className="flex items-center gap-2">
                    {/* Save button */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSave}
                      disabled={updateLoading || !hasUnsavedChanges}
                      className="flex items-center gap-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                    >
                      {updateLoading ? (
                        <Loader className="h-4 w-4 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4" />
                      )}
                      <span className="hidden sm:inline">
                        {updateLoading ? "Saving..." : "Save"}
                      </span>
                    </Button>

                    {/* Publish button */}
                    <Button
                      variant={
                        currentPage?.status === "published"
                          ? "secondary"
                          : "default"
                      }
                      size="sm"
                      onClick={handlePublish}
                      disabled={
                        publishLoading ||
                        updateLoading ||
                        currentPage?.status === "published"
                      }
                      className="flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                    >
                      {publishLoading ? (
                        <Loader className="h-4 w-4 animate-spin" />
                      ) : (
                        <Globe className="h-4 w-4" />
                      )}
                      <span className="hidden sm:inline">
                        {publishLoading
                          ? "Publishing..."
                          : currentPage?.status === "published"
                          ? "Published"
                          : "Publish"}
                      </span>
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Notion-style Main Content */}
        <main className="max-w-4xl mx-auto px-6 py-8">
          {/* Notion-style Title */}
          <div className="mb-4">
            <textarea
              value={title}
              onChange={(e) =>
                isPageOwner ? handleTitleChange(e.target.value) : undefined
              }
              onFocus={() => setTitleFocused(true)}
              onBlur={() => setTitleFocused(false)}
              placeholder="Untitled"
              readOnly={!isPageOwner}
              className={`w-full text-5xl font-bold text-gray-900 dark:text-gray-100 bg-transparent border-0 outline-0 resize-none placeholder-gray-400 dark:placeholder-gray-500 transition-all duration-200 ${
                titleFocused
                  ? "text-gray-900 dark:text-gray-100"
                  : "text-gray-900 dark:text-gray-100"
              } ${!isPageOwner ? "cursor-default" : ""}`}
              style={{
                lineHeight: "1.2",
                height: "auto",
                minHeight: "60px",
                overflow: "hidden",
                overflowWrap: "break-word",
                wordWrap: "break-word",
                whiteSpace: "pre-wrap",
                fontFamily:
                  'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
              }}
              onInput={(e) => {
                const target = e.target as HTMLTextAreaElement;
                // Reset height to auto to get the correct scrollHeight
                target.style.height = "auto";
                // Set height to scrollHeight to fit content
                target.style.height = Math.max(60, target.scrollHeight) + "px";
              }}
              ref={(textarea) => {
                if (textarea) {
                  // Auto-resize on initial load and when title changes
                  const resizeTextarea = () => {
                    textarea.style.height = "auto";
                    textarea.style.height = Math.max(60, textarea.scrollHeight) + "px";
                  };

                  // Resize immediately
                  resizeTextarea();

                  // Also resize after a short delay to handle any async content loading
                  setTimeout(resizeTextarea, 0);
                }
              }}
            />
          </div>

          {/* Notion-style Content Area */}
          <div className="notion-content-area w-full">
            {/* Only render editor when page data is fully ready to prevent race conditions */}
            {(() => {
              const timestamp = new Date().toISOString();
              const shouldRenderEditor = isPageDataReady && currentPage;

              console.log(`🔍 [Page Editor] [${timestamp}] RENDER DECISION:`, {
                isPageDataReady,
                hasCurrentPage: !!currentPage,
                shouldRenderEditor,
                loading,
                error,
                content,
                contentType: typeof content,
              });

              if (shouldRenderEditor) {
                console.log(`🔍 [Page Editor] [${timestamp}] RENDERING: YooptaRichTextEditor with content:`, content);
                return (
                  <YooptaRichTextEditor
                    content={content}
                    onChange={handleContentChange}
                    onSave={handleAutoSave}
                    autoSave={true}
                    autoSaveInterval={30000}
                    loading={autoSaveLoading}
                    placeholder={isPageOwner ? "Type '/' for commands..." : "This page is read-only"}
                    className="w-full"
                    pageId={typeof id === "string" ? id : undefined}
                    readOnly={!isPageOwner}
                  />
                );
              } else if (loading) {
                console.log(`🔍 [Page Editor] [${timestamp}] RENDERING: Loading state`);
                return (
                  <div className="w-full flex items-center justify-center py-20">
                    <div className="flex items-center gap-2 text-gray-500">
                      <Loader className="h-5 w-5 animate-spin" />
                      <span>Loading page content...</span>
                    </div>
                  </div>
                );
              } else if (error) {
                console.log(
                  `🔍 [Page Editor] [${timestamp}] RENDERING: Error state`
                );
                return (
                  <div className="w-full flex items-center justify-center py-20">
                    <div className="text-center">
                      <div className="text-red-600 mb-2">Failed to load page</div>
                      <div className="text-gray-500 text-sm">{error}</div>
                      <Button
                        onClick={() => {
                          if (id && typeof id === "string") {
                            dispatch(actions.fetchPageRequest(id));
                          }
                        }}
                        className="mt-4"
                        variant="outline"
                      >
                        Retry
                      </Button>
                    </div>
                  </div>
                );
              } else {
                console.log(
                  `🔍 [Page Editor] [${timestamp}] RENDERING: Preparing state`
                );
                return (
                  <div className="w-full flex items-center justify-center py-20">
                    <div className="text-gray-500">Preparing editor...</div>
                  </div>
                );
              }
            })()}
          </div>
        </main>
      </div>
    </>
  );
};

export default PageEditor;
