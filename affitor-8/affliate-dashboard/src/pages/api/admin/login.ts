import { NextApiRequest, NextApiResponse } from "next";
import { StrapiAdminClient } from "@/utils/request";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ error: "Email and password are required" });
    }

    // Use StrapiAdminClient for admin login
    const data = await StrapiAdminClient.adminLogin(email, password);

    // Return the admin data and token
    res.status(200).json(data);
  } catch (error: any) {
    console.error("Admin login API error:", error);

    // Handle different error types
    if (error.statusCode) {
      return res.status(error.statusCode).json({
        error: error.message || "Lo<PERSON> failed",
      });
    }

    res.status(500).json({ error: "Internal server error" });
  }
}
