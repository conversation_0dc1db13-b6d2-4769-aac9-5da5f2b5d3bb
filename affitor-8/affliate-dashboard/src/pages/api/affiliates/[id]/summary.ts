import { NextApiRequest, NextApiResponse } from "next";
import { AppError } from "@/interfaces";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";

interface AffiliateSummaryResponse {
  summary: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<AffiliateSummaryResponse | AppError>
) {
  if (req.method === "GET") {
    try {
      const { id } = req.query;

      if (!id || typeof id !== "string") {
        return res.status(400).json({ statusCode: 400, message: "Invalid ID" });
      }

      // Get auth token from request headers
      const token = req.headers.authorization?.split(" ")[1];
      if (!token) {
        return res.status(401).json({ statusCode: 401, message: "Unauthorized" });
      }

      const response: any = await StrapiClient.getAffiliateSummary(id, token);
      res.status(200).json(response);
    } catch (error: any) {
      console.error("Error fetching affiliate summary:", error);
      sendApiError(res, error, "Error fetching affiliate summary");
    }
  } else {
    res.setHeader("Allow", ["GET"]);
    res
      .status(405)
      .json({ statusCode: 405, message: `Method ${req.method} Not Allowed` });
  }
}
