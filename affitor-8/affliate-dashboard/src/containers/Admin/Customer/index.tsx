"use client";
import React, { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  actions as referralActions,
  selectReferrals,
  selectReferralsLoading,
  selectReferralsError,
  selectReferralsPagination,
} from "@/features/referral/referral.slice";
import {
  actions as referralCommissionActions,
  selectReferralCommissions,
  selectReferralCommissionsLoading,
  selectReferralCommissionsError,
  selectReferralCommissionsPagination,
} from "@/features/referral-commission/referral-commission.slice";
import { AppDispatch } from "@/store";
import CustomerTab from "./CustomerTab";
import CommisssionTab from "./CommisssionTab";
import TransactionTab from "./TransactionTab";

type CustomerTab = "customers" | "commissions" | "transactions";

export default function AdminCustomer() {
  const dispatch = useDispatch<AppDispatch>();
  const referrals = useSelector(selectReferrals);
  const loading = useSelector(selectReferralsLoading);
  const error = useSelector(selectReferralsError);
  const pagination = useSelector(selectReferralsPagination);

  // Commission tab state from Redux
  const commissions = useSelector(selectReferralCommissions);
  const commissionsLoading = useSelector(selectReferralCommissionsLoading);
  const commissionsError = useSelector(selectReferralCommissionsError);
  const commissionsPagination = useSelector(
    selectReferralCommissionsPagination
  );
  const [commissionsPage, setCommissionsPage] = useState(1);

  const [activeTab, setActiveTab] = useState<CustomerTab>("customers");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [mounted, setMounted] = useState(false);
  const [statusFilter, setStatusFilter] = useState<
    "all" | "conversion" | "lead" | "pending"
  >("all");
  const [sortField, setSortField] = useState<string>("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Advanced filter states
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    username: "",
    email: "",
    createdAtFrom: "",
    createdAtTo: "",
    totalPaidFrom: "",
    totalPaidTo: "",
    referrerId: "",
  });
  const [partners, setPartners] = useState<any[]>([]);
  const [allPartners, setAllPartners] = useState<any[]>([]);
  const [partnerSearchTerm, setPartnerSearchTerm] = useState("");

  // Fix hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch partners for dropdown
  useEffect(() => {
    const fetchPartners = async () => {
      try {
        const token = localStorage.getItem("admin_token");
        if (!token) return;

        const response = await fetch("/api/admin/partners?pageSize=100", {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          const partnersData = data.results || [];
          setAllPartners(partnersData);
          setPartners(partnersData);
        } else {
          console.error("Failed to fetch partners:", response.status, response.statusText);
        }
      } catch (error) {
        console.error("Error fetching partners:", error);
      }
    };

    if (mounted) {
      fetchPartners();
    }
  }, [mounted]);

  // Filter partners based on search term
  useEffect(() => {
    if (!partnerSearchTerm.trim()) {
      setPartners(allPartners);
    } else {
      const filtered = allPartners.filter((partner) => {
        const username = partner.user?.username?.toLowerCase() || "";
        const email = partner.user?.email?.toLowerCase() || "";
        const searchLower = partnerSearchTerm.toLowerCase();
        return username.includes(searchLower) || email.includes(searchLower);
      });
      setPartners(filtered);
    }
  }, [partnerSearchTerm, allPartners]);

  // Log when filters change to track dependency updates
  useEffect(() => {
    console.log("🔄 Filters state changed:", filters);
  }, [filters]);

  // Helper function to build filter parameters
  const buildFilterParams = useCallback(() => {
    console.log("🏗️ Building filter params with current filters:", filters);

    const params: any = {
      page: currentPage,
      pageSize: 10,
      search: searchTerm,
      status: statusFilter === "all" ? "" : statusFilter,
      isAdmin: true,
      sort: `${sortField}:${sortOrder.toUpperCase()}`,
    };

    // Add advanced filters if they have values
    if (filters.username) params.username = filters.username;
    if (filters.email) params.email = filters.email;
    if (filters.createdAtFrom) params.createdAtFrom = filters.createdAtFrom;
    if (filters.createdAtTo) params.createdAtTo = filters.createdAtTo;
    if (filters.totalPaidFrom) params.totalPaidFrom = filters.totalPaidFrom;
    if (filters.totalPaidTo) params.totalPaidTo = filters.totalPaidTo;
    if (filters.referrerId) {
      console.log("🎯 Adding referrerId filter:", filters.referrerId);
      params.referrerId = filters.referrerId;
    }

    console.log("📤 Final API params:", params);
    return params;
  }, [currentPage, searchTerm, statusFilter, sortField, sortOrder, filters]);

  // Fetch referrals on component mount and when page changes (but not search changes)
  useEffect(() => {
    if (mounted && activeTab === "customers" && !searchTerm) {
      dispatch(referralActions.fetchReferralsRequest(buildFilterParams()));
    }
  }, [dispatch, mounted, activeTab, searchTerm, buildFilterParams]);

  // Fetch commissions when tab is active
  useEffect(() => {
    if (mounted && activeTab === "commissions") {
      dispatch(
        referralCommissionActions.fetchCommissionsRequest({
          page: commissionsPage,
          pageSize: 10,
          isAdmin: true,
        })
      );
    }
  }, [mounted, activeTab, commissionsPage, dispatch]);

  // Handle search with debounce - this is the only place search API calls should happen
  useEffect(() => {
    if (!mounted || activeTab !== "customers") return;

    console.log("⏱️ Debounced effect triggered - will fetch referrals in 500ms");
    const timer = setTimeout(() => {
      console.log("🚀 Executing debounced API call");
      setCurrentPage(1); // Reset to first page when searching
      dispatch(referralActions.fetchReferralsRequest(buildFilterParams()));
    }, 500);

    return () => clearTimeout(timer);
  }, [dispatch, mounted, activeTab, buildFilterParams]);

  // Separate effect to handle page changes when there's a search term (no debounce needed)
  useEffect(() => {
    if (
      !mounted ||
      activeTab !== "customers" ||
      !searchTerm ||
      searchTerm.trim() === "" ||
      currentPage === 1
    )
      return;

    dispatch(referralActions.fetchReferralsRequest(buildFilterParams()));
  }, [dispatch, mounted, activeTab, searchTerm, currentPage, buildFilterParams]);

  const handleSort = (field: string) => {
    if (sortField === field) {
      // Toggle sort order if same field
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      // Set new field with default desc order
      setSortField(field);
      setSortOrder("desc");
    }
    setCurrentPage(1); // Reset to first page when sorting
  };

  // Filter handling functions
  const handleFilterChange = (field: string, value: string) => {
    console.log("🔄 Filter Change:", { field, value, previousFilters: filters });
    setFilters(prev => {
      const newFilters = {
        ...prev,
        [field]: value
      };
      console.log("📝 New Filters State:", newFilters);
      return newFilters;
    });
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleClearFilters = () => {
    setFilters({
      username: "",
      email: "",
      createdAtFrom: "",
      createdAtTo: "",
      totalPaidFrom: "",
      totalPaidTo: "",
      referrerId: "",
    });
    setCurrentPage(1);
  };

  if (!mounted) {
    return null;
  }

  return (
    <div className="p-3 sm:p-4 lg:p-6 bg-gray-50 dark:bg-gray-900 min-h-full">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Customers
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage customers, commissions, and transactions
            </p>
          </div>
          {/* <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
            Create customer
          </button> */}
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <nav className="flex space-x-1 bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm border border-gray-200 dark:border-gray-700">
          {[
            {
              key: "customers",
              label: "Referral",
              count: pagination?.total || 0,
            },
            { key: "commissions", label: "Commissions", count: 4 },
            { key: "transactions", label: "Transactions", count: 5 },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as CustomerTab)}
              className={`flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === tab.key
                  ? "bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400"
                  : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === "customers" && (
        <CustomerTab
          referrals={referrals}
          loading={loading}
          error={error}
          pagination={pagination}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          sortField={sortField}
          sortOrder={sortOrder}
          onSort={handleSort}
          showFilters={showFilters}
          setShowFilters={setShowFilters}
          filters={filters}
          handleFilterChange={handleFilterChange}
          handleClearFilters={handleClearFilters}
          partners={partners}
          partnerSearchTerm={partnerSearchTerm}
          setPartnerSearchTerm={setPartnerSearchTerm}
        />
      )}
      {activeTab === "commissions" && (
        <CommisssionTab
          commissions={commissions}
          commissionsLoading={commissionsLoading}
          commissionsError={commissionsError}
          commissionsPagination={commissionsPagination}
          commissionsPage={commissionsPage}
          setCommissionsPage={setCommissionsPage}
        />
      )}
      {activeTab === "transactions" && <TransactionTab />}
    </div>
  );
}
