"use client";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { actions as adminActions } from "@/features/admin/admin.slice";
import {
  selectAdminSettings,
  selectAdminSettingsLoading,
  selectAdminSettingsUpdating,
  selectAdminSettingsError,
} from "@/features/admin/admin.slice";
import { AppDispatch } from "@/store";

interface ProgramSettings {
  payoutCycle: string;
  minimumPayout: number;
  processingFee: number;
  reserveRate: number;
  cookieDuration: number;
  paymentMethods: {
    paypal: boolean;
    bankTransfer: boolean;
  };
}

const AdminSettings: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();

  // Redux state
  const settingsData = useSelector(selectAdminSettings);
  const isLoadingSettings = useSelector(selectAdminSettingsLoading); // For fetching settings on page load
  const isUpdatingSettings = useSelector(selectAdminSettingsUpdating); // For save button loading state
  const settingsError = useSelector(selectAdminSettingsError);

  // Local state for form data
  const [settings, setSettings] = useState<ProgramSettings>({
    payoutCycle: "biweekly",
    minimumPayout: 50,
    processingFee: 2.50,
    reserveRate: 10,
    cookieDuration: 60,
    paymentMethods: {
      paypal: true,
      bankTransfer: true,
    },
  });

  const [saveSuccess, setSaveSuccess] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Track when we initiate a save operation
  const isSavingRef = useRef(false);
  const previousUpdatingRef = useRef(false);

  // Payout cycle options
  const payoutCycleOptions = [
    { value: "weekly", label: "Weekly (mỗi tuần)" },
    { value: "biweekly", label: "Biweekly (mỗi 2 tuần)" },
    { value: "monthly", label: "Monthly (hàng tháng)" },
  ];

  // Load existing settings
  useEffect(() => {
    dispatch(adminActions.fetchSettings());
  }, [dispatch]);

  // Update local state when settings data is loaded
  useEffect(() => {
    if (settingsData) {
      setSettings(settingsData);
    }
  }, [settingsData]);

  // Handle input changes
  const handleInputChange = (field: keyof ProgramSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear any existing errors for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  // Handle payment method changes
  const handlePaymentMethodChange = (method: 'paypal' | 'bankTransfer', checked: boolean) => {
    setSettings(prev => ({
      ...prev,
      paymentMethods: {
        ...prev.paymentMethods,
        [method]: checked,
      },
    }));
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (settings.minimumPayout < 0) {
      newErrors.minimumPayout = "Minimum payout must be positive";
    }

    if (settings.processingFee < 0) {
      newErrors.processingFee = "Processing fee must be positive";
    }

    if (settings.reserveRate < 0 || settings.reserveRate > 100) {
      newErrors.reserveRate = "Reserve rate must be between 0 and 100";
    }

    if (settings.cookieDuration < 1) {
      newErrors.cookieDuration = "Cookie duration must be at least 1 day";
    }

    if (!settings.paymentMethods.paypal && !settings.paymentMethods.bankTransfer) {
      newErrors.paymentMethods = "At least one payment method must be enabled";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle save
  const handleSave = () => {
    if (!validateForm()) {
      return;
    }

    setSaveSuccess(false);
    setErrors({});

    // Mark that we're initiating a save operation
    isSavingRef.current = true;

    // Dispatch the update action - the saga will handle the async operation
    dispatch(adminActions.updateSettings(settings));
  };

  // Handle errors from Redux
  useEffect(() => {
    if (settingsError) {
      setErrors({ general: settingsError });
      // Reset saving flag if there's an error
      isSavingRef.current = false;
    }
  }, [settingsError]);

  // Handle success state from Redux
  useEffect(() => {
    // Track updating state changes
    const wasUpdating = previousUpdatingRef.current;
    previousUpdatingRef.current = isUpdatingSettings;

    // Check if we just finished a save operation (was updating, now not updating)
    if (wasUpdating && !isUpdatingSettings && isSavingRef.current) {
      isSavingRef.current = false; // Reset the save flag

      if (!settingsError) {
        // Success - no error and updating finished
        setSaveSuccess(true);
        setTimeout(() => setSaveSuccess(false), 3000);
      }
      // Error case is already handled by the error useEffect below
    }
  }, [isUpdatingSettings, settingsError]);

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <div className="flex items-center space-x-2 mb-2">
          <span className="text-lg">⚙️</span>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Settings
          </h1>
        </div>
        <p className="text-gray-600 dark:text-gray-400">
          Configure program settings and payment options.
        </p>
      </div>

      {/* Settings Form */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
            Program Settings
          </h2>

          <div className="space-y-6">
            {/* Payout Cycle */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Payout Cycle
              </label>
              <select
                value={settings.payoutCycle}
                onChange={(e) => handleInputChange("payoutCycle", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {payoutCycleOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Minimum Payout */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Minimum Payout
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400">
                  $
                </span>
                <input
                  type="number"
                  value={settings.minimumPayout}
                  onChange={(e) => handleInputChange("minimumPayout", parseFloat(e.target.value) || 0)}
                  className={`w-full pl-8 pr-3 py-2 border rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.minimumPayout ? "border-red-500" : "border-gray-300 dark:border-gray-600"
                  }`}
                  min="0"
                  step="0.01"
                />
              </div>
              {errors.minimumPayout && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.minimumPayout}</p>
              )}
            </div>

            {/* Processing Fee */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Processing Fee
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400">
                  $
                </span>
                <input
                  type="number"
                  value={settings.processingFee}
                  onChange={(e) => handleInputChange("processingFee", parseFloat(e.target.value) || 0)}
                  className={`w-full pl-8 pr-3 py-2 border rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.processingFee ? "border-red-500" : "border-gray-300 dark:border-gray-600"
                  }`}
                  min="0"
                  step="0.01"
                />
              </div>
              {errors.processingFee && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.processingFee}</p>
              )}
            </div>

            {/* Reserve Rate */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Reserve Rate
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={settings.reserveRate}
                  onChange={(e) => handleInputChange("reserveRate", parseFloat(e.target.value) || 0)}
                  className={`w-full pr-8 pl-3 py-2 border rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.reserveRate ? "border-red-500" : "border-gray-300 dark:border-gray-600"
                  }`}
                  min="0"
                  max="100"
                  step="0.1"
                />
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400">
                  %
                </span>
              </div>
              {errors.reserveRate && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.reserveRate}</p>
              )}
            </div>

            {/* Cookie Duration */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Cookie Duration
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={settings.cookieDuration}
                  onChange={(e) => handleInputChange("cookieDuration", parseInt(e.target.value) || 0)}
                  className={`w-full pr-16 pl-3 py-2 border rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.cookieDuration ? "border-red-500" : "border-gray-300 dark:border-gray-600"
                  }`}
                  min="1"
                />
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400">
                  days
                </span>
              </div>
              {errors.cookieDuration && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.cookieDuration}</p>
              )}
            </div>

            {/* Payment Methods */}
            {/* <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Payment Methods
              </label>
              <div className="space-y-3">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={settings.paymentMethods.paypal}
                    onChange={(e) => handlePaymentMethodChange("paypal", e.target.checked)}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Paypal</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={settings.paymentMethods.bankTransfer}
                    onChange={(e) => handlePaymentMethodChange("bankTransfer", e.target.checked)}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Bank Transfer</span>
                </label>
              </div>
              {errors.paymentMethods && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.paymentMethods}</p>
              )}
            </div> */}
          </div>

          {/* Error Message */}
          {errors.general && (
            <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
              <div className="flex items-center">
                <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400 mr-2" />
                <p className="text-sm text-red-600 dark:text-red-400">{errors.general}</p>
              </div>
            </div>
          )}

          {/* Success Message */}
          {saveSuccess && (
            <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400 mr-2" />
                <p className="text-sm text-green-600 dark:text-green-400">Settings saved successfully!</p>
              </div>
            </div>
          )}

          {/* Save Button */}
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={handleSave}
              disabled={isUpdatingSettings}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isUpdatingSettings ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Saving Settings...
                </>
              ) : (
                "Save Settings"
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSettings;
