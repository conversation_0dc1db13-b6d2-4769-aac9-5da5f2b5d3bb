import React, { useState, useEffect } from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { useSelector, useDispatch } from "react-redux";
import { trackLinksActions } from "@/features/rootActions";
import { RootState } from "@/store";
import QuickOverview from "./QuickOverview";
import PerformanceOverview from "./PerformanceOverview";
import LinksOverview from "./LinksOverview";
import RecentReferral from "./RecentReferral";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

type TimePeriod = "weekly" | "monthly" | "yearly";

const DashboardContainer: React.FC = () => {
  const [timePeriod, setTimePeriod] = useState<TimePeriod>("weekly");
  const dispatch = useDispatch();

  const performanceOverview = useSelector(
    (state: RootState) => state.trackLinks.performanceOverview
  );
  const performanceLoading = useSelector(
    (state: RootState) => state.trackLinks.performanceLoading
  );

  useEffect(() => {
    // Map time period to API format
    const periodMap = {
      weekly: "weekly",
      monthly: "monthly",
      yearly: "yearly",
    };

    dispatch(
      trackLinksActions.fetchPerformanceOverviewRequest({
        period: periodMap[timePeriod],
      })
    );
  }, [dispatch, timePeriod]);

  // Generate chart data from performance overview or use fallback
  const chartData = {
    labels: performanceOverview?.time || [
      "Apr 30",
      "May 07",
      "May 14",
      "May 21",
    ],
    datasets: [
      {
        label: "Visitors",
        data: performanceOverview?.visitors || [42, 55, 39, 65],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.5)",
        tension: 0.4,
      },
      {
        label: "Leads",
        data: performanceOverview?.leads || [15, 18, 14, 22],
        borderColor: "rgb(245, 158, 11)",
        backgroundColor: "rgba(245, 158, 11, 0.5)",
        tension: 0.4,
      },
      {
        label: "Conversions",
        data: performanceOverview?.conversions || [8, 12, 7, 15],
        borderColor: "rgb(16, 185, 129)",
        backgroundColor: "rgba(16, 185, 129, 0.5)",
        tension: 0.4,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          display: true,
        },
        border: {
          display: false,
        },
      },
      x: {
        grid: {
          display: false,
        },
        border: {
          display: false,
        },
      },
    },
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
      <div className="mb-6 sm:mb-8">
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-800 dark:text-white mb-2">
          Dashboard
        </h1>
        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
          Thanks for joining our affiliate program! If you have any questions,
          contact <EMAIL>
        </p>
      </div>

      {/* Quick overview cards */}
      <div className="mb-6 sm:mb-8">
        <QuickOverview />
      </div>

      {/* Performance chart */}
      <div className="mb-6 sm:mb-8">
        <PerformanceOverview />
      </div>

      {/* Links overview table */}
      <div className="mb-6 sm:mb-8">
        <LinksOverview />
      </div>

      {/* Recent referrals table */}
      <div className="mb-6 sm:mb-8">
        <RecentReferral />
      </div>
    </div>
  );
};

export default DashboardContainer;
