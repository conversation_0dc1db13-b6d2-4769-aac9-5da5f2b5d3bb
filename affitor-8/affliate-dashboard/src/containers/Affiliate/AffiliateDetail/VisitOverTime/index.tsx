import VisitData from "./VisitData";
import VisitChart from "./VisitChart";
import { useSelector } from "react-redux";
import {
  selectErrorTrafficWeb,
  selectTrafficWebList
} from "@/features/traffic-web/traffic-web.slice";
import { Loading } from "@/components";

export default function VisitOverTime() {
  const trafficWebs = useSelector(selectTrafficWebList);
  const error = useSelector(selectErrorTrafficWeb);

  return (
    <>
      {trafficWebs ? (
        <div className="h-fit flex flex-col">
          <div className="h-[250px]">
            {trafficWebs.length > 0 && trafficWebs[0].chart ? (
              <VisitChart
                labels={Object.keys(trafficWebs[0].chart)}
                data={Object.values(trafficWebs[0].chart)}
              />
            ) : (
              <p className="text-xs md:text-sm">Chart data is not available</p>
            )}
          </div>
          <div className="mt-6">
            {trafficWebs.length > 0 ? (
              <VisitData
                totalVisits={trafficWebs[0].visits}
                avgVisitDuration={trafficWebs[0].time_on_site}
                bounceRate={trafficWebs[0].bounce_rate}
                pagePerVisit={trafficWebs[0].page_per_visit}
              />
            ) : (
              <p className="text-xs md:text-sm">Statistic data is not available</p>
            )}
          </div>
        </div>
      ) : (
        !error && <Loading containerClassName="!h-[500px]" />
      )}
    </>
  );
}
