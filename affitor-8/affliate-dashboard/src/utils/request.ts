import { IAff<PERSON>te, <PERSON><PERSON>eta, ISocial, ITrafficWeb } from "@/interfaces";
import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from "axios";
import qs from "qs";
import { getR<PERSON><PERSON><PERSON><PERSON><PERSON>ie, REFERRAL_COOKIE_NAME } from "./cookies";

export class API {
  private readonly client: AxiosInstance;

  constructor(config: AxiosRequestConfig) {
    // Set withCredentials to true by default to include cookies in all requests
    const configWithCredentials = {
      ...config,
      withCredentials: true,
    };

    this.client = axios.create(configWithCredentials);
    this.client.interceptors.request.use(
      function (config: InternalAxiosRequestConfig) {
        config.timeout = 30000;
        config.withCredentials = true;

        // Auto-attach token from localStorage if available
        if (typeof window !== "undefined") {
          const token = localStorage.getItem("auth_token");
          if (token && !config.headers.Authorization) {
            config.headers.Authorization = `Bearer ${token}`;
          }
        }

        return config;
      },
      function (error: any) {
        console.log("❌ request interceptors", error);
        return Promise.reject({
          message: error.message,
          statusCode: error.status,
        });
      }
    );
  }

  post(
    path: string,
    data: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse["data"]> {
    return new Promise(async (resolve, reject) => {
      try {
        const res = await this.client.post(path, data, config);
        return resolve(res.data);
      } catch (error: any) {
        return reject({
          message: error.response?.data?.error?.message || error.message,
          statusCode:
            error.response?.data?.error?.status || error.response?.status,
        });
      }
    });
  }

  put(
    path: string,
    data: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse["data"]> {
    return new Promise(async (resolve, reject) => {
      try {
        const res = await this.client.put(path, data, config);
        return resolve(res.data);
      } catch (error: any) {
        return reject({
          message: error.response?.data?.error?.message || error.message,
          statusCode:
            error.response?.data?.error?.status || error.response?.status,
        });
      }
    });
  }

  get(path: string, config: AxiosRequestConfig) {
    return new Promise(async (resolve, reject) => {
      try {
        const res = await this.client.get(path, config);
        return resolve(res.data);
      } catch (error: any) {
        console.log("❌ request interceptors", error);
        return reject({
          message: error.response?.data?.error?.message || error.message,
          statusCode:
            error.response?.data?.error?.status || error.response?.status,
        });
      }
    });
  }

  delete(
    path: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse["data"]> {
    return new Promise(async (resolve, reject) => {
      try {
        const res = await this.client.delete(path, config);
        return resolve(res.data);
      } catch (error: any) {
        return reject({
          message: error.response?.data?.error?.message || error.message,
          statusCode:
            error.response?.data?.error?.status || error.response?.status,
        });
      }
    });
  }
}

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

export const StrapiAdminClient = {
  client: new API({
    baseURL: BASE_URL,
    headers: {
      "Content-Type": "application/json",
    },
  }),

  // Admin Authentication
  adminLogin: async function (email: string, password: string) {
    try {
      return StrapiAdminClient.client.post(
        "/admin/login",
        { email, password },
        {}
      );
    } catch (error: any) {
      console.error("Admin login error:", error);
      throw error;
    }
  },

  // Admin Payout Management
  getAdminPayouts: async function (
    query: {
      status?: "pending" | "approved" | "completed";
      page?: number;
      pageSize?: number;
      search?: string;
    },
    token: string
  ) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      // Build query parameters for Strapi content manager
      const queryParams = new URLSearchParams({
        page: (query.page || 1).toString(),
        pageSize: (query.pageSize || 10).toString(),
        sort: "id:ASC",
      });

      // Add population for referrer and user data
      queryParams.append("populate[referrer][populate][user]", "*");

      // Add status filter if provided
      if (query.status) {
        queryParams.append(
          "filters[$and][0][payout_status][$eq]",
          query.status
        );
      }

      // Add search filter if provided
      if (query.search) {
        queryParams.append(
          "filters[$and][1][$or][0][referrer][user][email][$containsi]",
          query.search
        );
        queryParams.append(
          "filters[$and][1][$or][1][referrer][user][first_name][$containsi]",
          query.search
        );
        queryParams.append(
          "filters[$and][1][$or][2][referrer][user][last_name][$containsi]",
          query.search
        );
        queryParams.append(
          "filters[$and][1][$or][3][referrer][referral_code][$containsi]",
          query.search
        );
        queryParams.append(
          "filters[$and][1][$or][4][id][$containsi]",
          query.search
        );
      }

      return StrapiAdminClient.client.get(
        `/content-manager/collection-types/api::payout.payout?${queryParams}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error: any) {
      console.error("Get admin payouts error:", error);
      throw error;
    }
  },

  approvePayout: async function (documentId: string, token: string) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      return StrapiAdminClient.client.put(
        `/content-manager/collection-types/api::payout.payout/${documentId}`,
        {
          payout_status: "approved",
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error: any) {
      console.error("Approve payout error:", error);
      throw error;
    }
  },

  markPayoutAsPaid: async function (
    documentIds: string | string[],
    token: string
  ) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      const ids = Array.isArray(documentIds) ? documentIds : [documentIds];

      // For bulk operations, we'll need to update each payout individually
      // or create a custom endpoint. For now, let's update them individually
      const updatePromises = ids.map((documentId) =>
        StrapiAdminClient.client.put(
          `/content-manager/collection-types/api::payout.payout/${documentId}?populate[referrer][populate][user]=*`,
          {
            payout_status: "completed",
            payout_date: new Date().toISOString(),
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        )
      );

      const results = await Promise.all(updatePromises);

      // Transform the raw Strapi response to match frontend expectations
      const transformedData = results.map((result) => {
        const payout = result.data;
        return {
          id: payout.id,
          documentId: payout.documentId,
          payout_status: payout.payout_status,
          payment_method: payout.method,
          commission_cycle: "monthly",
          amount: payout.amount,
          processing_fee: payout.processing_fee,
          payout_date: payout.payout_date,
          createdAt: payout.createdAt,
          updatedAt: payout.updatedAt,
          publishedAt: payout.publishedAt,
          // Map referrer to partner structure expected by frontend
          partner: payout.referrer
            ? {
                id: payout.referrer.id,
                documentId: payout.referrer.documentId,
                referral_code: payout.referrer.referral_code,
                referrer_status: payout.referrer.referrer_status,
                user: payout.referrer.user
                  ? {
                      id: payout.referrer.user.id,
                      username:
                        payout.referrer.user.username ||
                        payout.referrer.referral_code,
                      email: payout.referrer.user.email,
                      first_name: payout.referrer.user.first_name,
                      last_name: payout.referrer.user.last_name,
                    }
                  : {
                      id: payout.referrer.id,
                      username:
                        payout.referrer.referral_code ||
                        `user-${payout.referrer.id}`,
                      email: `referrer-${payout.referrer.id}@example.com`,
                      first_name: "Unknown",
                      last_name: "User",
                    },
              }
            : null,
          referrer: payout.referrer,
          status: payout.status,
        };
      });

      return { data: transformedData };
    } catch (error: any) {
      console.error("Mark payout as paid error:", error);
      throw error;
    }
  },

  archivePayout: async function (documentId: string, token: string) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      return StrapiAdminClient.client.put(
        `/content-manager/collection-types/api::payout.payout/${documentId}`,
        {
          archived: true,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error: any) {
      console.error("Archive payout error:", error);
      throw error;
    }
  },

  exportPayouts: async function (
    query: {
      status?: "pending" | "approved" | "completed";
      format?: "csv" | "excel";
      search?: string;
    },
    token: string
  ) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      // For export, we'll fetch the data and format it client-side
      // First, get all payouts matching the criteria
      const exportQuery = {
        status: query.status,
        page: 1,
        pageSize: 1000, // Get a large number for export
        search: query.search,
      };

      const response: any = await StrapiAdminClient.getAdminPayouts(
        exportQuery,
        token
      );

      // Return the data with format info for client-side processing
      return {
        data: response.data,
        meta: response.meta,
        format: query.format || "csv",
      };
    } catch (error: any) {
      console.error("Export payouts error:", error);
      throw error;
    }
  },

  createPayout: async function (
    payoutData: {
      partnerId: string;
      amount: number;
      method: "paypal" | "bank transfer";
      payout_status: "pending" | "approved" | "completed";
    },
    token: string
  ) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      // Create the payout using Strapi's content manager API
      return StrapiAdminClient.client.post(
        "/content-manager/collection-types/api::payout.payout",
        {
          payout_status: payoutData.payout_status,
          method: payoutData.method,
          amount: payoutData.amount,
          referrer: {
            connect: [
              {
                documentId: payoutData.partnerId,
              },
            ],
          },
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error: any) {
      console.error("Create payout error:", error);
      throw error;
    }
  },

  // Admin Partners Management
  getPartners: async function (
    query: {
      page?: number;
      pageSize?: number;
      search?: string;
      status?: string;
      sort?: string;
      username?: string;
      email?: string;
      createdAtFrom?: string;
      createdAtTo?: string;
      revenueFrom?: string;
      revenueTo?: string;
      commissionFrom?: string;
      commissionTo?: string;
      clicksFrom?: string;
      clicksTo?: string;
      leadsFrom?: string;
      leadsTo?: string;
      conversionsFrom?: string;
      conversionsTo?: string;
    },
    token: string
  ) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      // Build query parameters for Strapi
      const queryParams = new URLSearchParams({
        page: (query.page || 1).toString(),
        pageSize: (query.pageSize || 10).toString(),
        "populate[user]": "username,email,first_name,last_name",
        "populate[referrer_links][count]": "true",
        "populate[referrals][count]": "true",
        "populate[referral_commissions][count]": "true",
        "populate[payouts][count]": "true",
      });

      // Add search filter if provided
      if (query.search) {
        queryParams.append(
          "filters[$or][0][user][email][$containsi]",
          query.search
        );
        queryParams.append(
          "filters[$or][1][user][first_name][$containsi]",
          query.search
        );
        queryParams.append(
          "filters[$or][2][user][last_name][$containsi]",
          query.search
        );
        queryParams.append(
          "filters[$or][3][referral_code][$containsi]",
          query.search
        );
      }

      // Add status filter if provided
      if (query.status) {
        queryParams.append("filters[referrer_status][$eq]", query.status);
      }

      // Add sort parameter if provided
      if (query.sort) {
        queryParams.append("sort", query.sort);
      }

      // Add advanced filters
      let filterIndex = query.search ? 4 : 0; // Continue from where search filters left off

      if (query.username) {
        queryParams.append(`filters[$and][${filterIndex}][user][username][$containsi]`, query.username);
        filterIndex++;
      }

      if (query.email) {
        queryParams.append(`filters[$and][${filterIndex}][user][email][$containsi]`, query.email);
        filterIndex++;
      }

      if (query.createdAtFrom) {
        queryParams.append(`filters[$and][${filterIndex}][createdAt][$gte]`, query.createdAtFrom);
        filterIndex++;
      }

      if (query.createdAtTo) {
        queryParams.append(`filters[$and][${filterIndex}][createdAt][$lte]`, query.createdAtTo);
        filterIndex++;
      }

      if (query.revenueFrom) {
        queryParams.append(`filters[$and][${filterIndex}][total_revenue][$gte]`, query.revenueFrom);
        filterIndex++;
      }

      if (query.revenueTo) {
        queryParams.append(`filters[$and][${filterIndex}][total_revenue][$lte]`, query.revenueTo);
        filterIndex++;
      }

      if (query.commissionFrom) {
        queryParams.append(`filters[$and][${filterIndex}][total_earnings][$gte]`, query.commissionFrom);
        filterIndex++;
      }

      if (query.commissionTo) {
        queryParams.append(`filters[$and][${filterIndex}][total_earnings][$lte]`, query.commissionTo);
        filterIndex++;
      }



      if (query.clicksFrom) {
        queryParams.append(`filters[$and][${filterIndex}][totalClicks][$gte]`, query.clicksFrom);
        filterIndex++;
      }

      if (query.clicksTo) {
        queryParams.append(`filters[$and][${filterIndex}][totalClicks][$lte]`, query.clicksTo);
        filterIndex++;
      }

      if (query.leadsFrom) {
        queryParams.append(`filters[$and][${filterIndex}][totalLeads][$gte]`, query.leadsFrom);
        filterIndex++;
      }

      if (query.leadsTo) {
        queryParams.append(`filters[$and][${filterIndex}][totalLeads][$lte]`, query.leadsTo);
        filterIndex++;
      }

      if (query.conversionsFrom) {
        queryParams.append(`filters[$and][${filterIndex}][totalConversions][$gte]`, query.conversionsFrom);
        filterIndex++;
      }

      if (query.conversionsTo) {
        queryParams.append(`filters[$and][${filterIndex}][totalConversions][$lte]`, query.conversionsTo);
        filterIndex++;
      }

      return StrapiAdminClient.client.get(
        `/content-manager/collection-types/api::referrer.referrer?${queryParams}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error: any) {
      console.error("Error fetching partners:", error);
      throw error;
    }
  },

  // Update Partner Status
  updatePartnerStatus: async function (
    partnerId: string,
    status: string,
    token: string
  ) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      return StrapiAdminClient.client.put(
        `/content-manager/collection-types/api::referrer.referrer/${partnerId}`,
        {
          data: {
            referrer_status: status,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error: any) {
      console.error("Error updating partner status:", error);
      throw error;
    }
  },

  // Get Partner Details
  getPartnerDetails: async function (partnerId: string, token: string) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      const queryParams = new URLSearchParams({
        "populate[user]": "username,email,first_name,last_name",
        "populate[referrer_links]": "*",
        "populate[referrals]": "id,documentId",
        "populate[referral_commissions]": "",
        "populate[payouts]": "*",
      });

      return StrapiAdminClient.client.get(
        `/content-manager/collection-types/api::referrer.referrer/${partnerId}?${queryParams}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error: any) {
      console.error("Error fetching partner details:", error);
      throw error;
    }
  },

  // Delete Partner
  deletePartner: async function (partnerId: string, token: string) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      return StrapiAdminClient.client.delete(
        `/content-manager/collection-types/api::referrer.referrer/${partnerId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error: any) {
      console.error("Error deleting partner:", error);
      throw error;
    }
  },

  // Get Admin Dashboard Stats
  getDashboardStats: async function (token: string) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      // This would typically be a custom endpoint in Strapi
      return StrapiAdminClient.client.get(
        "/api/admin/affiliate-program/dashboard",
        {
          headers: {
            // Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error: any) {
      console.error("Error fetching dashboard stats:", error);
      throw error;
    }
  },

  // Admin method to create referrer link for a specific partner
  createReferrerLinkForPartner: async function (
    linkData: {
      name: string;
      url: string;
      shortLink?: string;
      userId: string;
      userDocumentId: string;
      referrerId: string;
      referrerDocumentId: string;
    },
    token: string
  ) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      // Create the referrer link using Strapi's content manager API
      return StrapiAdminClient.client.post(
        "/content-manager/collection-types/api::referrer-link.referrer-link",
        {
          name: linkData.name,
          url: linkData.url,
          short_link: linkData.shortLink,
          user: {
            connect: [
              {
                id: parseInt(linkData.userId),
                documentId: linkData.userDocumentId,
              },
            ],
          },
          referrer: {
            connect: [
              {
                id: parseInt(linkData.referrerId),
                documentId: linkData.referrerDocumentId,
              },
            ],
          },
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error: any) {
      console.error("Error creating referrer link for partner:", error);
      throw error;
    }
  },

  // Admin method to fetch users (Customers)
  getUsers: async function (
    query: {
      page?: number;
      pageSize?: number;
      search?: string;
      sort?: string;
    },
    token: string
  ) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      const { page = 1, pageSize = 10, search = "", sort = "id:ASC" } = query;

      const queryParams = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        sort: sort.toString(),
      });

      if (search && search.trim()) {
        queryParams.append("filters[$or][0][email][$containsi]", search);
        queryParams.append("filters[$or][1][first_name][$containsi]", search);
        queryParams.append("filters[$or][2][last_name][$containsi]", search);
      }

      // Populate related data (explicit fields, not *)
      queryParams.append("populate[user][fields][0]", "id");
      queryParams.append("populate[user][fields][1]", "email");
      queryParams.append("populate[user][fields][2]", "first_name");
      queryParams.append("populate[user][fields][3]", "last_name");
      queryParams.append("populate[user][fields][4]", "documentId");
      queryParams.append("populate[referrer][fields][0]", "id");
      queryParams.append("populate[referrer][fields][1]", "referral_code");
      queryParams.append("populate[referrer][fields][2]", "documentId");
      queryParams.append(
        "populate[referrer][populate][user][fields][0]",
        "first_name"
      );
      queryParams.append(
        "populate[referrer][populate][user][fields][1]",
        "last_name"
      );
      queryParams.append(
        "populate[referrer][populate][user][fields][2]",
        "email"
      );
      queryParams.append("populate[referrer_link][fields][0]", "id");
      queryParams.append("populate[referrer_link][fields][1]", "name");
      queryParams.append("populate[referrer_link][fields][2]", "url");
      queryParams.append("populate[referrer_link][fields][3]", "documentId");
      queryParams.append("populate[referral_activities][count]", "true");
      queryParams.append("populate[referral_commissions][count]", "true");

      const strapiPath = `/content-manager/collection-types/plugin::users-permissions.user?${queryParams}`;

      return await StrapiAdminClient.client.get(strapiPath, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      console.error("Error fetching admin users:", error);
      throw error;
    }
  },

  // Admin method to fetch referrals (Customers)
  getReferrals: async function (
    query: {
      page?: number;
      pageSize?: number;
      search?: string;
      sort?: string;
      username?: string;
      email?: string;
      createdAtFrom?: string;
      createdAtTo?: string;
      totalPaidFrom?: string;
      totalPaidTo?: string;
      referrerId?: string;
      status?: string;
    },
    token: string
  ) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      const {
        page = 1,
        pageSize = 10,
        search = "",
        sort = "id:ASC",
        username,
        email,
        createdAtFrom,
        createdAtTo,
        totalPaidFrom,
        totalPaidTo,
        referrerId,
        status
      } = query;

      const queryParams = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        sort: sort.toString(),
      });

      let filterIndex = 0;

      // Add search filter (by user email, username, etc.)
      if (search && search.trim()) {
        queryParams.append(`filters[$or][${filterIndex}][user][email][$containsi]`, search);
        queryParams.append(`filters[$or][${filterIndex + 1}][user][username][$containsi]`, search);
        filterIndex += 2;
      }

      // Add advanced filters
      if (username && username.trim()) {
        queryParams.append("filters[user][username][$containsi]", username);
      }

      if (email && email.trim()) {
        queryParams.append("filters[user][email][$containsi]", email);
      }

      if (status && status !== "all") {
        queryParams.append("filters[referral_status][$eq]", status);
      }

      if (referrerId && referrerId.trim()) {
        console.log("🎯 StrapiAdminClient - Adding referrer filter:", referrerId);
        queryParams.append("filters[referrer][id][$eq]", referrerId);
      }

      // Add date range filters
      if (createdAtFrom) {
        queryParams.append("filters[createdAt][$gte]", createdAtFrom);
      }

      if (createdAtTo) {
        queryParams.append("filters[createdAt][$lte]", createdAtTo);
      }

      // Add total paid range filters
      if (totalPaidFrom) {
        queryParams.append("filters[total_paid][$gte]", totalPaidFrom);
      }

      if (totalPaidTo) {
        queryParams.append("filters[total_paid][$lte]", totalPaidTo);
      }

      // Populate related data
      queryParams.append(
        "populate[user]",
        "username,email,first_name,last_name"
      );
      queryParams.append(
        "populate[referrer][populate][user]",
        "username,email"
      );
      queryParams.append("populate[referrer_link]", "*");
      queryParams.append("populate[referral_activities][count]", "true");
      queryParams.append("populate[referral_commissions][count]", "true");

      const strapiPath = `/content-manager/collection-types/api::referral.referral?${queryParams}`;

      console.log("🌐 StrapiAdminClient - Final Strapi URL:", strapiPath);
      console.log("🔍 StrapiAdminClient - Query params string:", queryParams.toString());

      return await StrapiAdminClient.client.get(strapiPath, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      console.error("Error fetching admin referrals:", error);
      throw error;
    }
  },

  // Add this method for admin referral commissions
  getReferralCommissions: async function (
    query: {
      page?: number;
      pageSize?: number;
      sort?: string;
      search?: string;
    },
    token: string
  ) {
    if (!token) {
      throw new Error("Admin authentication required");
    }
    // Build query string for Strapi admin endpoint
    const params = new URLSearchParams();
    params.append("page", String(query.page || 1));
    params.append("populate[referrer][populate][user]", "username,email");
    params.append("populate[referral][populate][user]", "username,email");
    params.append("populate[subscription_tier]", "id,name,display_name");
    params.append("pageSize", String(query.pageSize || 10));
    params.append("sort", query.sort || "id:ASC");
    // Strapi admin does not support search for this collection by default, but keep for future
    // if (query.search) params.append("search", query.search);

    return await StrapiAdminClient.client.get(
      `/content-manager/collection-types/api::referral-commission.referral-commission?${params.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
  },

  // Admin method to fetch the list of transactions for the referral
  getReferralTransaction: async function (
    query: {
      page?: number;
      pageSize?: number;
      sort?: string;
      search?: string;
    },
    token: string
  ) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      const {
        page = 1,
        pageSize = 10,
        sort = "createdAt:DESC",
        search = "",
      } = query;

      const queryParams = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        sort: sort.toString(),
        isFromAdmin: "1",
      });

      // Add search filter if provided
      if (search && search.trim()) {
        queryParams.append("filters[$or][0][user][email][$containsi]", search);
        queryParams.append(
          "filters[$or][1][user][username][$containsi]",
          search
        );
        queryParams.append(
          "filters[$or][2][stripe_checkout_session][$containsi]",
          search
        );
        queryParams.append("filters[$or][3][id][$containsi]", search);
      }

      // Populate related data
      queryParams.append(
        "populate[user][populate][referral][populate][referrer]",
        "*"
      );
      queryParams.append("populate[subscription_tier]", "*");
      queryParams.append("populate[child_transactions][count]", "true");
      queryParams.append("populate[parent_transaction]", "*");

      return await StrapiAdminClient.client.get(
        `/content-manager/collection-types/api::transaction.transaction?${queryParams}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
    } catch (error: any) {
      console.error("Error fetching referral transactions:", error);
      throw error;
    }
  },

  // Admin Settings Management
  getCommissionConfig: async function (token: string) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      const endpoint = "/content-manager/single-types/api::commission-config.commission-config";
      console.log("🌐 Making request to:", `${BASE_URL}${endpoint}`);
      console.log("🔑 Using token:", token ? `${token.substring(0, 10)}...` : "No token");

      const response: any = await StrapiAdminClient.client.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      console.log("📡 Raw response from getCommissionConfig:", {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data
      });

      return response;
    } catch (error: any) {
      console.error("❌ Error in getCommissionConfig:", error);
      console.error("❌ Error details:", {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url
      });
      throw error;
    }
  },

  updateCommissionConfig: async function (
    settings: {
      payout_cycle: string;
      minimum_payout: number;
      processing_fee: number;
      reverse_rate: number;
      cookie_duration: number;
    },
    token: string
  ) {
    try {
      if (!token) {
        throw new Error("Admin authentication required");
      }

      return StrapiAdminClient.client.post(
        "/content-manager/single-types/api::commission-config.commission-config/actions/publish",
        settings,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error: any) {
      console.error("Error updating commission config:", error);
      throw error;
    }
  },
};

export const StrapiClient = {
  client: new API({
    baseURL: BASE_URL,
    headers: {
      "Content-Type": "application/json",
    },
  }),

  getCategories: async function () {
    return StrapiClient.client.get("/api/categories", {});
  },

  getCountries: async function () {
    const res: any = await StrapiClient.client.get(
      "/api/affiliates?fields=country",
      {}
    );
    const countries = Array.from(
      new Set((res.data || []).map((a: any) => a.country).filter(Boolean))
    );
    return countries;
  },

  getLaunchYears: async function () {
    const res: any = await StrapiClient.client.get(
      "/api/affiliates?fields=launch_year",
      {}
    );
    const years = Array.from(
      new Set((res.data || []).map((a: any) => a.launch_year).filter(Boolean))
    ).sort();
    return years;
  },

  getAffiliateTags: async function () {
    return StrapiClient.client.get("/api/tags?populate=icon", {});
  },

  getAffiliates: async function (
    query: string,
    token?: string,
    cookies?: string
  ): Promise<{ data: IAffiliate[]; meta: IMeta } | any> {
    const headers: any = {};
    // Only override token if explicitly provided
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    if (cookies) {
      headers.Cookie = cookies;
    }
    return StrapiClient.client.get(`/api/affiliates?${query}`, { headers });
  },

  getAffiliateById: async function (id: string) {
    const query = qs.stringify({
      populate: {
        payment_methods: {
          populate: "image",
          fields: ["id", "name"],
        },
        commission: { fields: ["id", "max_percentage", "title"] },
        image: { fields: ["id", "url"] },
        tags: {
          populate: "icon",
          fields: ["id", "name"],
        },
        categories: {
          fields: ["id", "name", "slug"],
        },
        industry: {
          fields: ["id", "name", "slug"],
        },
      },
    });
    console.log("LOG-query", query);
    return StrapiClient.client.get(`/api/affiliates/${id}?${query}`, {});
  },

  initSocialData: async function ({
    platforms,
    affiliateDocId,
  }: {
    platforms: string[];
    affiliateDocId: string;
  }) {
    const query = `platforms=${platforms.join(",")}`;
    return StrapiClient.client.get(
      `/api/social-listenings/${affiliateDocId}?${query}`,
      {}
    );
  },

  getSocialData: async function (
    query: string,
    token?: string,
    cookies?: string
  ): Promise<{ data: ISocial[]; meta: IMeta } | any> {
    const headers: any = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    if (cookies) {
      headers.Cookie = cookies;
      console.log(`🍪 Forwarding cookies to getSocialData: ${cookies}`);
    }
    return StrapiClient.client.get(`/api/social-listenings?${query}`, {
      headers,
    });
  },

  getTrafficWeb: async function (
    query: string
  ): Promise<{ data: ITrafficWeb[]; meta: IMeta } | any> {
    return StrapiClient.client.get(`/api/traffic-webs?${query}`, {});
  },

  getGoogleAuthUrl: async function () {
    try {
      return StrapiClient.client.get("/api/auth/google/url", {});
    } catch (error) {
      console.error("Error getting Google auth URL:", error);
      throw error;
    }
  },

  authenticateWithFirebaseToken: async function (
    idToken: string,
    cookies?: string
  ) {
    try {
      const headers: any = {};
      if (cookies) {
        headers.Cookie = cookies;
        console.log(
          `🍪 Forwarding cookies to Firebase authentication: ${cookies}`
        );
      }

      return StrapiClient.client.post(
        "/api/auth/firebase/authenticate",
        { idToken },
        { headers }
      );
    } catch (error) {
      console.error("Firebase token authentication error:", error);
      throw error;
    }
  },

  validateToken: async function (token: string) {
    return StrapiClient.client.get("/api/auth/validate-token", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  },

  signin: async function (email: string, password: string) {
    try {
      return StrapiClient.client.post(
        "/api/auth/signin",
        { email, password },
        {}
      );
    } catch (error: any) {
      console.error("Sign-in error request:", error);
      throw error;
    }
  },

  signup: async function (userData: {
    fullName: string;
    email: string;
    password: string;
  }) {
    try {
      return StrapiClient.client.post("/api/auth/signup", userData, {});
    } catch (error: any) {
      console.error("Sign-up error:", error);
      throw error;
    }
  },



  // Updated AIScript API method to include promptId
  sendScriptMessage: async function (
    message: string,
    token: string,
    promptId?: number,
    sessionId?: string
  ) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      // Create request body with message and promptId if available
      const requestBody: any = { script: message };
      if (promptId) {
        requestBody.promptId = promptId;
      }

      if (sessionId) {
        requestBody.sessionId = sessionId;
      }

      // Send the request to our API proxy for scripts
      return StrapiClient.client.post("/api/aiscripts/process", requestBody, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error: any) {
      console.error("Script message error:", error);
      throw error;
    }
  },

  // Discourse SSO methods
  getDiscourseConfig: async function () {
    return StrapiClient.client.get("/api/discourse/config", {});
  },

  // Get the direct login URL for Discourse (forward flow)
  getDiscourseLoginUrl: async function () {
    return StrapiClient.client.get("/api/discourse/login-url", {});
  },

  // Process SSO parameters from Discourse (reverse flow)
  processDiscourseSSOParams: async function (token: string, sso: string, sig: string) {
    if (!token) {
      throw new Error("Authentication required");
    }

    if (!sso || !sig) {
      throw new Error("SSO parameters are required");
    }

    // Build query parameters with SSO parameters
    const queryParams = new URLSearchParams();
    queryParams.append('sso', sso);
    queryParams.append('sig', sig);

    const queryString = queryParams.toString();
    const url = `/api/discourse/sso-url?${queryString}`;

    return StrapiClient.client.get(url, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  },

  endScriptSession: async function (token: string, sessionId?: string) {
    if (!token) {
      throw new Error("Authentication required");
    }

    return StrapiClient.client.post(
      `/api/aiscript-sessions/end`,
      {
        sessionId,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
  },

  getPrompts: async function (token: string) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      return StrapiClient.client.get("/api/prompts", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error: any) {
      console.error("Error fetching prompts:", error);
      throw error;
    }
  },

  getSocialListeningTranscript: async function (
    videoId: string,
    token?: string,
    cookies?: string
  ): Promise<any> {
    const headers: any = {};
    // Only override token if explicitly provided
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    if (cookies) {
      headers.Cookie = cookies;
    }
    return StrapiClient.client.get(
      `/api/social-listenings/transcript/${videoId}`,
      { headers }
    );
  },

  getTikTokVideoUrl: async function (videoId: string): Promise<any> {
    try {
      const response: any = await StrapiClient.client.get(
        `/api/social-listenings/tiktok-download/${videoId}`,
        {}
      );
      console.log("TikTok video URL response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error fetching TikTok video URL:", error);
      throw error;
    }
  },

  getPaymentMethods: async function () {
    return StrapiClient.client.get("/api/payment-methods", {});
  },

  getAffiliateUrl: async (id: string) => {
    try {
      return StrapiClient.client.get(`/api/affiliates/${id}/url`, {});
    } catch (error) {
      console.error("Error fetching affiliate URL:", error);
      throw error;
    }
  },

  getAffiliateSummary: async (id: string, token: string) => {
    try {
      const response: any = await StrapiClient.client.get(
        `/api/affiliates/${id}/summary`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response;
    } catch (error) {
      console.error("Error fetching affiliate summary:", error);
      throw error;
    }
  },

  getUserMe: async function (token: string, cookieString?: string) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      const headers: any = {
        Authorization: `Bearer ${token}`,
      };

      // If cookies are provided, forward them all to Strapi
      if (cookieString) {
        headers.Cookie = cookieString;
        console.log(`🍪 Forwarding all cookies to Strapi: ${cookieString}`);
      }

      return StrapiClient.client.get("/api/users/me", {
        headers,
      });
    } catch (error) {
      console.error("Error fetching user data:", error);
      throw error;
    }
  },

  getSubscriptionTiers: async function (token?: string, cookies?: string) {
    try {
      const headers: any = {};
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }
      if (cookies) {
        headers.Cookie = cookies;
        console.log(
          `🍪 Forwarding cookies to getSubscriptionTiers: ${cookies}`
        );
      }
      return StrapiClient.client.get(
        "/api/subscription-tiers?sort=price&populate=tier_features",
        { headers }
      );
    } catch (error) {
      console.error("Error fetching subscription tiers:", error);
      throw error;
    }
  },

  subscribeToTier: async function (
    tierId: string,
    token: string,
    paymentMethod: string,
    billingPeriod?: string
  ) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      return StrapiClient.client.post(
        "/api/subscription-tiers/subscribe",
        { tierId, paymentMethod, billingPeriod },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error) {
      console.error("Error subscribing to tier:", error);
      throw error;
    }
  },

  createCheckoutSession: async function (tierId: string, token?: string) {
    const headers: any = {};
    // Only override token if explicitly provided
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return StrapiClient.client.post(
      "/api/subscription-tiers/create-checkout-session",
      { tierId },
      { headers }
    );
  },

  // Add new method for confirming checkout session
  confirmCheckoutSession: async function (
    sessionId: string,
    token?: string,
    cookies?: string
  ) {
    const headers: any = {};
    // Only override token if explicitly provided
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    if (cookies) {
      headers.Cookie = cookies;
      console.log(
        `🍪 Forwarding cookies to confirmCheckoutSession: ${cookies}`
      );
    }

    return StrapiClient.client.get(
      `/api/subscription-tiers/confirm-checkout/${sessionId}`,
      { headers }
    );
  },

  getComparisonPlans: async function (token?: string | null) {
    try {
      const params: any = {};
      if (token) {
        params.headers = {
          Authorization: `Bearer ${token}`,
        };
      }

      return StrapiClient.client.get(
        "/api/subscription-tiers/compare-plans",
        params
      );
    } catch (error) {
      console.error("Error fetching comparison plans:", error);
      throw error;
    }
  },

  cancelSubscription: async function (token?: string) {
    const headers: any = {};
    // Only override token if explicitly provided
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return StrapiClient.client.post(
      "/api/subscription-tiers/cancel",
      {},
      { headers }
    );
  },

  getTopAds: async function (
    query: string,
    token?: string,
    cookies?: string
  ): Promise<{ data: any[]; meta: IMeta } | any> {
    const headers: any = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    if (cookies) {
      headers.Cookie = cookies;
      console.log(`🍪 Forwarding cookies to getTopAds: ${cookies}`);
    }
    return StrapiClient.client.get(`/api/top-ads?${query}`, { headers });
  },

  // Add new method for getting ad transcript
  getAdTranscript: async function (adId: string, token?: string): Promise<any> {
    const headers: any = {};
    // Only override token if explicitly provided
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    return StrapiClient.client.get(`/api/top-ads/transcript/${adId}`, {
      headers,
    });
  },

  updateUserProfile: async function (
    profileData: {
      first_name?: string;
      last_name?: string;
      address?: string;
      apt?: string;
      city?: string;
      state?: string;
      country?: string;
      zip_code?: string;
      paypal_email?: string;
      bank_transfer?: {
        account_number?: string;
        swift_code?: string;
        first_name?: string;
        last_name?: string;
        business_name?: string;
        country?: string;
        city?: string;
        state?: string;
        address?: string;
        zip_code?: string;
      };
    },
    token: string
  ) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      // Call our proxy API endpoint instead of the Strapi API directly
      return axios.put("/api/users/profile", profileData, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });
    } catch (error) {
      console.error("Error updating user profile:", error);
      throw error;
    }
  },

  getYoutubeTranscript: async function (
    videoId: string,
    token?: string
  ): Promise<{ transcript: string; source: string; error?: string }> {
    try {
      if (!videoId) {
        throw new Error("Video ID is required");
      }

      // Use the backend API endpoint to get the transcript
      const params: any = {};
      if (token) {
        params.headers = {
          Authorization: `Bearer ${token}`,
        };
      }

      const result: any = await StrapiClient.client.get(
        `/api/social-listenings/custom-transcript/${videoId}`,
        params
      );

      // Format the response to match TranscriptResponse interface
      return {
        transcript: result.transcript || "",
        source: result.source || "api",
        error: result.error || undefined,
      };
    } catch (error: any) {
      console.error("Error fetching YouTube transcript:", error);
      return {
        transcript: "",
        source: "error",
        error: error.message || "Failed to fetch transcript",
      };
    }
  },

  // New method to register a user as a referrer
  registerReferrer: async function (token: string) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      return StrapiClient.client.post(
        "/api/referrers/register",
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error) {
      console.error("Error registering referrer:", error);
      throw error;
    }
  },

  // Enhanced method to track referral link clicks with priority logic
  trackReferralClick: async function (url: string, token?: string, shortLink?: string) {
    try {
      const headers: any = {};
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      // Prepare request body with both URL and shortLink for priority tracking
      const requestBody: { url?: string; shortLink?: string } = {};
      if (url) requestBody.url = url;
      if (shortLink) requestBody.shortLink = shortLink;

      return StrapiClient.client.post(
        "/api/referrer-links/track-click",
        requestBody,
        { headers }
      );
    } catch (error) {
      console.error("Error tracking referral click:", error);
      throw error;
    }
  },

  // Referrer Links methods
  getReferrerLinks: async function (
    query: string,
    token: string
  ): Promise<any> {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      return StrapiClient.client.get(`/api/referrer-links?${query}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      console.error("Error fetching referrer links:", error);
      throw error;
    }
  },

  createReferrerLink: async function (
    linkData: { name: string; url: string; shortLink?: string; page_id?: string },
    token: string
  ) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      return StrapiClient.client.post("/api/referrer-links", linkData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      console.error("Error creating referrer link:", error);
      throw error;
    }
  },

  updateReferrerLink: async function (
    documentId: string,
    linkData: { name: string; url: string; shortLink?: string; selectedPage?: string },
    token: string
  ) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      // Prepare the data object, including short_link if provided
      const dataToUpdate: any = {
        name: linkData.name,
        url: linkData.url,
      };

      // Only include short_link if it's provided
      if (linkData.shortLink !== undefined) {
        dataToUpdate.short_link = linkData.shortLink;
      }

      // Include page_id if selectedPage is provided
      if (linkData.selectedPage && linkData.selectedPage.trim() !== "") {
        dataToUpdate.page_id = linkData.selectedPage;
      }

      // Use documentId instead of id for the URL path
      return StrapiClient.client.put(
        `/api/referrer-links/${documentId}`,
        { data: dataToUpdate }, // Wrap data in a 'data' object as required by Strapi
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error) {
      console.error("Error updating referrer link:", error);
      throw error;
    }
  },

  deleteReferrerLink: async function (documentId: string, token: string) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      // Use DELETE method to match the saga implementation
      return StrapiClient.client.delete(`/api/referrer-links/${documentId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      console.error("Error deleting referrer link:", error);
      throw error;
    }
  },

  getAvailablePagesForReferrerLink: async function (token: string) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      return StrapiClient.client.get("/api/pages/available-for-referrer-link", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      console.error("Error fetching available pages for referrer link:", error);
      throw error;
    }
  },

  trackPageView: async function (
    pageId: string,
    trackingData: { referrer?: string; source_type?: string },
    token?: string
  ) {
    try {
      const headers: any = {};
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      return StrapiClient.client.post(
        `/api/pages/track-view/${pageId}`,
        trackingData,
        { headers }
      );
    } catch (error) {
      console.error("Error tracking page view:", error);
      throw error;
    }
  },

  // Track Links methods
  getTrackLinksOverview: async function (token?: string) {
    try {
      const headers: any = {};
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      return StrapiClient.client.get("/api/track-links/overview", { headers });
    } catch (error) {
      console.error("Error fetching track links overview:", error);
      throw error;
    }
  },

  // Referral Commissions methods
  getReferralCommissions: async function (query: string, token: string) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      return StrapiClient.client.get(`/api/referral-commissions?${query}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      console.error("Error fetching referral commissions:", error);
      throw error;
    }
  },

  getReferralCommissionStats: async function (token: string) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      return StrapiClient.client.get("/api/referral-commissions/stats", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      console.error("Error fetching referral commission stats:", error);
      throw error;
    }
  },

  // Referrals methods
  getReferrals: async function (query: string, token: string) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      return StrapiClient.client.get(`/api/referrals?${query}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      console.error("Error fetching referrals:", error);
      throw error;
    }
  },

  // Referral Activities methods
  getReferralActivities: async function (query: string, token: string) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      return StrapiClient.client.get(`/api/referral-activities?${query}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      console.error("Error fetching referral activities:", error);
      throw error;
    }
  },

  // Payout methods
  getPayoutOverview: async function (token: string) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      return StrapiClient.client.get("/api/payouts?type=overview", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      console.error("Error fetching payout overview:", error);
      throw error;
    }
  },

  getPayoutHistory: async function (query: string, token: string) {
    try {
      if (!token) {
        throw new Error("Authentication required");
      }

      return StrapiClient.client.get(`/api/payouts?${query}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      console.error("Error fetching payout history:", error);
      throw error;
    }
  },

  // SpyHero methods
  searchSpyHeroAds: async function (
    keyword: string,
    platform: string,
    token?: string
  ) {
    try {
      console.log(
        "Searching SpyHero ads with keyword:",
        keyword,
        "on platform:",
        platform
      );
      const headers: any = {};
      // if (token) {
      //   headers.Authorization = `Bearer ${token}`;
      // }

      return StrapiClient.client.post(
        "/api/spyhero-ad/crawl-ads-with-keyword",
        { keyword, platform },
        { headers }
      );
    } catch (error) {
      console.error("Error searching SpyHero ads:", error);
      throw error;
    }
  },
  // Get referrer link by short code (for public access)
  getReferrerLinkByShortCode: async function (shortCode: string) {
    try {
      if (!shortCode) {
        throw new Error("Short code is required");
      }

      return StrapiClient.client.get(
        `/api/referrer-links/short/${shortCode}`,
        {}
      );
    } catch (error) {
      console.error("Error fetching referrer link by short code:", error);
      throw error;
    }
  },
};
