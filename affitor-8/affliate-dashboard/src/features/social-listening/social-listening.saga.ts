import { call, put, takeEvery } from "redux-saga/effects";
import { actions } from "./social-listening.slice";
import { PayloadAction } from "@reduxjs/toolkit";
import { IPagination, ISort } from "@/interfaces";
import qs from "qs";
import { actions as aiscriptActions } from "@/features/aiscript/aiscript.slice";
import { handleApiError } from "@/utils/error-handler";

function* handleFetch(
  action: PayloadAction<{
    platforms: string[];
    affiliateDocId: string;
    pagination: IPagination;
    sort: ISort | ISort[];
  }>
): Generator<any, void, any> {
  try {
    yield put(actions.setLoading(true));
    const { platforms, affiliateDocId, pagination, sort } = action.payload;

    // Get token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    const headers: Record<string, string> = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const filters = {
      platform: { $in: platforms },
      affiliate: {
        documentId: affiliateDocId,
      },
    };

    const query = qs.stringify(
      {
        filters,
        pagination: {
          page: pagination.page,
          pageSize: pagination.pageSize || 10,
        },
        sort: [sort].flat().map((s) => `${s.field}:${s.order}`),
      },
      {
        encodeValuesOnly: true,
      }
    );

    // Simplified - no manual token handling
    const response: any = yield call(fetch, `/api/social-listenings?${query}`, {
      headers,
    });
    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        // set is loading fail
        yield put(actions.setLoading(false));
        return; // Error was handled
      }

      yield put(
        actions.setError(`Request failed with status ${response.status}`)
      );
      return;
    }

    const { data, meta } = yield response.json();

    if (!data || !meta) {
      yield put(actions.setError("Invalid response structure"));
      return;
    }

    yield put(actions.setSocialListenings(data));
    yield put(actions.setLoading(false));
    yield put(actions.setPagination(meta.pagination));
  } catch (error: any) {
    yield put(actions.setError("Failed to fetch social data"));
  } finally {
    yield put(actions.setLoading(false));
  }
}

// Updated handler for transcript fetching with authentication
function* handleFetchTranscript(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    yield put(actions.setLoadingTranscript(true));
    const videoId = action.payload;

    // Get token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    if (!token) {
      yield put(actions.setError("Authentication required. Please log in."));
      yield put(actions.setLoadingTranscript(false));
      return;
    }

    // Simplified - no manual token handling
    const response: any = yield call(
      fetch,
      `/api/social-listenings/transcript/${videoId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        // set is loading false
        yield put(actions.setLoadingTranscript(false));
        return; // Error was handled
      }

      yield put(
        actions.setError(`Failed to fetch transcript: ${response.status}`)
      );
      yield put(actions.setLoadingTranscript(false));
      return;
    }

    const data = yield response.json();
    const { transcript, suggestions, sessionId } = data;

    if (!transcript) {
      yield put(
        actions.setError("No transcript available for this video content")
      );
      yield put(actions.setLoadingTranscript(false));
      return;
    }

    if (sessionId) {
      yield put(aiscriptActions.setSessionId(sessionId));
    }

    yield put(aiscriptActions.clearMessages());
    yield put(actions.setTranscript(`Original script:\n` + transcript));

    // Add the transcript to the AIScript as an AI message with copyable flag
    yield put(
      aiscriptActions.setMessage({
        type: "ai",
        content: `Original script:\n` + transcript,
        copyable: true, // Mark this message as copyable
      })
    );

    // Open the AIScript UI using the global state action
    yield put(aiscriptActions.openAIScript());

    if (suggestions && suggestions.length > 0) {
      yield put(
        aiscriptActions.setMessage({
          type: "ai",
          content: "How would you like me to optimize your video transcript?",
          quickReplies: suggestions.map((suggestion: any) => ({
            label: suggestion.title,
            content: sessionId
              ? `Question: ${suggestion.content}`
              : `Context: ${transcript}\n
          Question: ${suggestion.content}
          `,
            promptId: suggestion.id,
          })),
        })
      );
    }

    yield put(actions.setLoadingTranscript(false));
  } catch (error: any) {
    console.log("Transcript error", error);
    yield put(actions.setLoadingTranscript(false));
  }
}

export default function* socialListeningSaga() {
  yield takeEvery(actions.fetch.type, handleFetch);
  yield takeEvery(actions.fetchTranscript.type, handleFetchTranscript);
}
