import { call, put, takeLatest, takeEvery } from "redux-saga/effects";
import { actions } from "./subscription-tier.slice";
import axios from "axios";

function* handleFetchSubscriptionTiers(): Generator<any, void, any> {
  try {
    // Get token from localStorage
    // const token =
    //   typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    // if (!token) {
    //   throw new Error("Authentication required. Please log in.");
    // }

    const response: Response = yield call(fetch, "/api/subscription-tiers", {
      // headers: {
        // Authorization: `Bearer ${token}`,
      // },
    });

    if (!response.ok) {
      const errorMsg = `Failed to fetch subscription tiers: ${response.status} ${response.statusText}`;
      console.error(errorMsg);
      yield put(actions.fetchSubscriptionTiersFailure(errorMsg));
      return;
    }

    const data = yield response.json();
    yield put(actions.fetchSubscriptionTiersSuccess(data));
  } catch (error: any) {
    const errorMsg = error.message || "Failed to fetch subscription tiers";
    console.error(errorMsg);
    yield put(actions.fetchSubscriptionTiersFailure(errorMsg));
  }
}

function* handleSubscribeTier(
  action: ReturnType<typeof actions.subscribeTier>
): Generator<any, void, any> {
  try {
    const { tierId } = action.payload;

    // Get token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    if (!token) {
      throw new Error("Authentication required. Please log in.");
    }

    const response: Response = yield call(
      fetch,
      `/api/subscription-tiers/subscribe`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ tierId }),
      }
    );

    if (!response.ok) {
      const errorMsg = `Failed to subscribe to tier: ${response.status} ${response.statusText}`;
      console.error(errorMsg);
      yield put(actions.subscribeTierFailure(errorMsg));
      return;
    }

    const data = yield response.json();

    // If we have a checkout URL, redirect to it
    if (data.checkoutUrl) {
      window.location.href = data.checkoutUrl;
    } else {
      if (data.transaction) {
        window.location.href = `/profile/upgrade/success?transaction_id=${data.transaction.id}`;
      }
    }

    yield put(actions.subscribeTierSuccess(data));
  } catch (error: any) {
    const errorMsg = error.message || "Failed to subscribe to tier";
    console.error(errorMsg);
    yield put(actions.subscribeTierFailure(errorMsg));
  }
}

// Function to fetch comparison plans data
const fetchComparisonPlansData = async (token: string | null) => {
  const headers: Record<string, string> = {};

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await axios.get("/api/subscription-tiers/compare-plans", {
    headers,
  });

  return response.data;
};

// Handler for fetching comparison plans
function* handleFetchComparisonPlans(): Generator<any, void, any> {
  try {
    // Get token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    // Call API to get comparison plans
    const comparePlans = yield call(fetchComparisonPlansData, token);

    // Store comparison plans in the store
    yield put(actions.setComparisonPlans(comparePlans));
  } catch (error: any) {
    console.error("Error fetching comparison plans:", error);
    const errorMessage = error.message || "Failed to fetch comparison plans";
    yield put(actions.setComparisonPlansError(errorMessage));
  }
}

// Handler for canceling subscription
function* handleCancelSubscription(): Generator<any, void, any> {
  try {
    // Get token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    if (!token) {
      throw new Error("Authentication required. Please log in.");
    }

    // Call the API to cancel the subscription
    const response: Response = yield call(
      fetch,
      "/api/subscription-tiers/cancel",
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      const errorMsg = `Failed to cancel subscription: ${response.status} ${response.statusText}`;
      console.error(errorMsg);
      yield put(actions.cancelSubscriptionFailure(errorMsg));
      return;
    }

    // Successfully canceled
    yield put(actions.cancelSubscriptionSuccess());

    // Refetch user data to update UI
    yield put({ type: "user/fetchUserMe" });
  } catch (error: any) {
    const errorMsg = error.message || "Failed to cancel subscription";
    console.error(errorMsg);
    yield put(actions.cancelSubscriptionFailure(errorMsg));
  }
}

export default function* subscriptionTierSaga() {
  yield takeLatest(
    actions.fetchSubscriptionTiers.type,
    handleFetchSubscriptionTiers
  );
  yield takeLatest(actions.subscribeTier.type, handleSubscribeTier);
  yield takeEvery(actions.fetchComparisonPlans, handleFetchComparisonPlans);
  yield takeLatest(actions.cancelSubscription, handleCancelSubscription);
}
