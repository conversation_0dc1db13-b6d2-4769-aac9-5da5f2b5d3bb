import React from "react";
import { Edit, Copy, Check } from "lucide-react";
import { ReferrerLink } from "@/features/referrer-links/referrer-links.slice";

interface Pagination {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

interface TableLinksProps {
  links: ReferrerLink[];
  isLoading: boolean;
  onEdit: (link: ReferrerLink) => void;
  onDelete: (link: ReferrerLink) => void;
  onCopy: (url: string, id: string, type: 'page' | 'shortLink' | 'url') => void;
  copiedId?: string | null;
  copiedType?: 'page' | 'shortLink' | 'url' | null;
  // Pagination props
  enablePagination?: boolean;
  pagination?: Pagination | null;
  currentPage?: number;
  onPageChange?: (page: number) => void;
  isPaginationLoading?: boolean;
}

// Expandable URL component with copy functionality
const ExpandableUrl: React.FC<{
  url: string;
  onCopy: () => void;
  isCopied: boolean;
}> = ({ url, onCopy, isCopied }) => {
  return (
    <div className="relative flex items-center gap-2">
      <span
        onClick={onCopy}
        className={`cursor-pointer text-blue-600 hover:text-blue-800 dark:text-blue-400 hover:underline break-all transition-colors duration-200 ${
          isCopied ? 'text-green-600 dark:text-green-400' : ''
        }`}
        title="Click to copy URL"
      >
        {url}
      </span>
      <button
        onClick={onCopy}
        className={`flex-shrink-0 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 ${
          isCopied ? 'text-green-600 dark:text-green-400' : 'text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300'
        }`}
        title="Copy URL"
      >
        {isCopied ? (
          <Check className="w-3 h-3" />
        ) : (
          <Copy className="w-3 h-3" />
        )}
      </button>
      {isCopied && (
        <span className="absolute -top-8 left-0 bg-gray-800 text-white text-xs px-2 py-1 rounded shadow-lg z-10">
          Copied!
        </span>
      )}
    </div>
  );
};

const TableLinks: React.FC<TableLinksProps> = ({
  links,
  isLoading,
  onEdit,
  onDelete,
  onCopy,
  copiedId,
  copiedType,
  enablePagination = false,
  pagination,
  currentPage = 1,
  onPageChange,
  isPaginationLoading = false,
}) => {
  return (
    <>
      <div className="overflow-x-auto relative">
        {/* Loading overlay similar to PerformanceOverview */}
        {isLoading && (
          <div className="absolute inset-0 bg-gray-50 dark:bg-gray-700 bg-opacity-50 flex items-center justify-center z-10">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        )}

        <table className="w-full table-fixed min-w-[800px]">
          <colgroup>
            <col className="w-32 sm:w-40" />
            <col className="w-32 sm:w-40" />
            <col className="w-32 sm:w-40" />
            <col className="w-48 sm:w-64" />
            <col className="w-20" />
            <col className="w-20" />
            <col className="w-24" />
            <col className="w-24" />
          </colgroup>
          <thead className="bg-gray-50 dark:bg-gray-700 text-xs sm:text-sm text-gray-500 dark:text-gray-400">
            <tr>
              <th className="px-3 sm:px-6 py-3 text-left">Link Name</th>
              <th className="px-3 sm:px-6 py-3 text-left">Linked Page</th>
              <th className="px-2 sm:px-3 py-3 text-left">Short Link</th>
              <th className="px-3 sm:px-6 py-3 text-left">URL</th>
              <th className="px-2 sm:px-3 py-3 text-center">Visitors</th>
              <th className="px-2 sm:px-3 py-3 text-center">Leads</th>
              <th className="px-2 sm:px-3 py-3 text-center">Conversions</th>
              <th className="px-3 sm:px-6 py-3 text-center">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100 dark:divide-gray-700">
            {links.map((link) => {
              return (
                <tr
                  key={link.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <td className="px-6 py-4">
                    <span className="font-medium text-gray-900 dark:text-white truncate block">
                      {link.name}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        {link.page ? (
                          <span className="text-blue-600 dark:text-blue-400 text-sm font-medium truncate block">
                            {link.page.title}
                          </span>
                        ) : (
                          <span className="text-gray-500 dark:text-gray-400 text-sm italic">
                            No page linked
                          </span>
                        )}
                      </div>
                      {link.page && (
                        <div className="relative group">
                          <button
                            onClick={() =>
                              onCopy(
                                `${
                                  process.env.NEXT_PUBLIC_AFFILIATE_BASE_URL ||
                                  "https://affitor.com"
                                }/editor/${link.page!.documentId}`,
                                link.id,
                                "page"
                              )
                            }
                            className="ml-2 p-1.5 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md flex-shrink-0 transition-all duration-200 hover:scale-105 hover:shadow-md active:scale-95"
                            title="Copy page link"
                          >
                            {copiedId === link.id && copiedType === "page" ? (
                              <Check className="w-3.5 h-3.5 text-green-500" />
                            ) : (
                              <Copy className="w-3.5 h-3.5 text-gray-400 dark:text-gray-500 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200" />
                            )}
                          </button>
                          {copiedId === link.id && copiedType === "page" && (
                            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-green-600 text-white text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap z-10 animate-in fade-in slide-in-from-bottom-2 duration-200">
                              ✓ Successfully copied!
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-3 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        {link.short_link ? (
                          <a
                            href={`${
                              process.env.NEXT_PUBLIC_AFFILIATE_BASE_URL ||
                              "https://affitor.com"
                            }/${link.short_link}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 hover:underline break-all"
                          >
                            {link.short_link}
                          </a>
                        ) : (
                          <span className="text-gray-500 dark:text-gray-400 text-sm italic">
                            No short link
                          </span>
                        )}
                      </div>
                      {link.short_link && (
                        <div className="relative group">
                          <button
                            onClick={() =>
                              onCopy(
                                `${
                                  process.env.NEXT_PUBLIC_AFFILIATE_BASE_URL ||
                                  "https://affitor.com"
                                }/${link.short_link}`,
                                link.id,
                                "shortLink"
                              )
                            }
                            className="ml-2 p-1.5 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md flex-shrink-0 transition-all duration-200 hover:scale-105 hover:shadow-md active:scale-95"
                            title="Copy short link"
                          >
                            {copiedId === link.id &&
                            copiedType === "shortLink" ? (
                              <Check className="w-3.5 h-3.5 text-green-500" />
                            ) : (
                              <Copy className="w-3.5 h-3.5 text-gray-400 dark:text-gray-500 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200" />
                            )}
                          </button>
                          {copiedId === link.id &&
                            copiedType === "shortLink" && (
                              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-green-600 text-white text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap z-10 animate-in fade-in slide-in-from-bottom-2 duration-200">
                                ✓ Successfully copied!
                              </div>
                            )}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <a
                          href={link.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 hover:underline break-all"
                        >
                          {link.url}
                        </a>
                      </div>
                      <div className="relative group">
                        <button
                          onClick={() => onCopy(link.url, link.id, "url")}
                          className="ml-2 p-1.5 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md flex-shrink-0 transition-all duration-200 hover:scale-105 hover:shadow-md active:scale-95"
                          title="Copy URL"
                        >
                          {copiedId === link.id && copiedType === "url" ? (
                            <Check className="w-3.5 h-3.5 text-green-500" />
                          ) : (
                            <Copy className="w-3.5 h-3.5 text-gray-400 dark:text-gray-500 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200" />
                          )}
                        </button>
                        {copiedId === link.id && copiedType === "url" && (
                          <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-green-600 text-white text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap z-10 animate-in fade-in slide-in-from-bottom-2 duration-200">
                            ✓ Successfully copied!
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-3 py-4 text-center text-sm">
                    {link.visitors}
                  </td>
                  <td className="px-3 py-4 text-center text-sm">
                    {link.leads}
                  </td>
                  <td className="px-3 py-4 text-center text-sm text-green-600">
                    {link.conversions}
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex justify-center space-x-2">
                      <button
                        onClick={() => onEdit(link)}
                        className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                        title="Edit link"
                      >
                        <Edit className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                      </button>
                    </div>
                  </td>
                </tr>
              );
            })}
            {links.length === 0 && !isLoading && (
              <tr>
                <td
                  colSpan={8}
                  className="px-6 py-8 text-center text-gray-500 dark:text-gray-400"
                >
                  No links created yet. Click "Create Link" to get started.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination Controls */}
      {enablePagination && links.length > 0 && pagination && onPageChange && (
        <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 relative">
          {/* Pagination loading overlay */}
          {isPaginationLoading && (
            <div className="absolute inset-0 bg-gray-50 dark:bg-gray-700 bg-opacity-50 flex items-center justify-center z-10">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            </div>
          )}

          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1 || isPaginationLoading}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => onPageChange(currentPage + 1)}
              disabled={
                currentPage >= pagination.pageCount || isPaginationLoading
              }
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Showing page{" "}
                <span className="font-medium">{pagination.page}</span> of{" "}
                <span className="font-medium">{pagination.pageCount}</span> (
                {pagination.total} total items)
              </p>
            </div>
            <div>
              <nav
                className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                aria-label="Pagination"
              >
                <button
                  onClick={() => onPageChange(currentPage - 1)}
                  disabled={currentPage === 1 || isPaginationLoading}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
                >
                  <span className="sr-only">Previous</span>
                  <svg
                    className="h-5 w-5"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>

                {/* Dynamic page numbers */}
                {Array.from(
                  { length: Math.min(5, pagination.pageCount) },
                  (_, i) => {
                    let pageNum;
                    if (pagination.pageCount <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= pagination.pageCount - 2) {
                      pageNum = pagination.pageCount - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => onPageChange(pageNum)}
                        disabled={isPaginationLoading}
                        className={`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium ${
                          currentPage === pageNum
                            ? "text-blue-600 dark:text-blue-400"
                            : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600"
                        } disabled:opacity-50`}
                      >
                        {pageNum}
                      </button>
                    );
                  }
                )}

                <button
                  onClick={() => onPageChange(currentPage + 1)}
                  disabled={
                    currentPage >= pagination.pageCount || isPaginationLoading
                  }
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
                >
                  <span className="sr-only">Next</span>
                  <svg
                    className="h-5 w-5"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default TableLinks;
