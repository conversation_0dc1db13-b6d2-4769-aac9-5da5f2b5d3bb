import { useState, useCallback, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actions, selectPanelWidth, selectIsMaximized } from '@/features/aiscript/aiscript.slice';

interface UseResizePanelOptions {
  minWidth?: number;
  maxWidth?: number;
  defaultWidth?: number;
}

export function useResizePanel(options: UseResizePanelOptions = {}) {
  const dispatch = useDispatch();
  const panelWidth = useSelector(selectPanelWidth);
  const isMaximized = useSelector(selectIsMaximized);
  
  const {
    minWidth = 320,
    maxWidth = 800,
    defaultWidth = 380
  } = options;

  const [isDragging, setIsDragging] = useState(false);
  const [dragStartX, setDragStartX] = useState(0);
  const [dragStartWidth, setDragStartWidth] = useState(0);
  const [currentWidth, setCurrentWidth] = useState(panelWidth);
  
  const mouseMoveRef = useRef<((e: MouseEvent) => void) | null>(null);
  const mouseUpRef = useRef<((e: MouseEvent) => void) | null>(null);
  const touchMoveRef = useRef<((e: TouchEvent) => void) | null>(null);
  const touchEndRef = useRef<((e: TouchEvent) => void) | null>(null);

  // Update current width when Redux state changes
  useEffect(() => {
    setCurrentWidth(panelWidth);
  }, [panelWidth]);

  // Calculate dynamic constraints based on viewport
  const getConstraints = useCallback(() => {
    const viewportWidth = typeof window !== 'undefined' ? window.innerWidth : 1200;
    const dynamicMaxWidth = Math.min(maxWidth, viewportWidth * 0.45);
    return { 
      minWidth, 
      maxWidth: dynamicMaxWidth 
    };
  }, [minWidth, maxWidth]);

  // Handle mouse move during drag
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;
    
    const deltaX = dragStartX - e.clientX; // Negative because we're resizing from the left
    const newWidth = dragStartWidth + deltaX;
    const { minWidth: min, maxWidth: max } = getConstraints();
    
    // Apply constraints
    const constrainedWidth = Math.max(min, Math.min(newWidth, max));
    setCurrentWidth(constrainedWidth);
  }, [isDragging, dragStartX, dragStartWidth, getConstraints]);

  // Handle mouse up to end drag
  const handleMouseUp = useCallback(() => {
    if (!isDragging) return;
    
    setIsDragging(false);
    
    // Update Redux state with final width
    dispatch(actions.setPanelWidth(currentWidth));
    
    // Clean up global event listeners
    if (mouseMoveRef.current) {
      document.removeEventListener('mousemove', mouseMoveRef.current);
      mouseMoveRef.current = null;
    }
    if (mouseUpRef.current) {
      document.removeEventListener('mouseup', mouseUpRef.current);
      mouseUpRef.current = null;
    }
    
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, [isDragging, currentWidth, dispatch]);

  // Handle touch move during drag
  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!isDragging) return;
    
    e.preventDefault();
    const touch = e.touches[0];
    const deltaX = dragStartX - touch.clientX;
    const newWidth = dragStartWidth + deltaX;
    const { minWidth: min, maxWidth: max } = getConstraints();
    
    const constrainedWidth = Math.max(min, Math.min(newWidth, max));
    setCurrentWidth(constrainedWidth);
  }, [isDragging, dragStartX, dragStartWidth, getConstraints]);

  // Handle touch end to end drag
  const handleTouchEnd = useCallback(() => {
    if (!isDragging) return;
    
    setIsDragging(false);
    dispatch(actions.setPanelWidth(currentWidth));
    
    // Clean up touch event listeners
    if (touchMoveRef.current) {
      document.removeEventListener('touchmove', touchMoveRef.current);
      touchMoveRef.current = null;
    }
    if (touchEndRef.current) {
      document.removeEventListener('touchend', touchEndRef.current);
      touchEndRef.current = null;
    }
  }, [isDragging, currentWidth, dispatch]);

  // Store refs for cleanup
  useEffect(() => {
    mouseMoveRef.current = handleMouseMove;
    mouseUpRef.current = handleMouseUp;
    touchMoveRef.current = handleTouchMove;
    touchEndRef.current = handleTouchEnd;
  }, [handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);

  // Start drag operation
  const startDrag = useCallback((clientX: number) => {
    if (isMaximized) return false; // Don't allow resize when maximized
    
    setIsDragging(true);
    setDragStartX(clientX);
    setDragStartWidth(currentWidth);
    
    // Add global event listeners
    if (mouseMoveRef.current && mouseUpRef.current) {
      document.addEventListener('mousemove', mouseMoveRef.current);
      document.addEventListener('mouseup', mouseUpRef.current);
    }
    
    document.body.style.cursor = 'ew-resize';
    document.body.style.userSelect = 'none';
    
    return true;
  }, [currentWidth, isMaximized]);

  // Start touch drag operation
  const startTouchDrag = useCallback((clientX: number) => {
    if (isMaximized) return false;
    
    setIsDragging(true);
    setDragStartX(clientX);
    setDragStartWidth(currentWidth);
    
    // Add global touch event listeners
    if (touchMoveRef.current && touchEndRef.current) {
      document.addEventListener('touchmove', touchMoveRef.current, { passive: false });
      document.addEventListener('touchend', touchEndRef.current);
    }
    
    return true;
  }, [currentWidth, isMaximized]);

  // Handle window resize to adjust max width
  useEffect(() => {
    const handleResize = () => {
      const { maxWidth: max } = getConstraints();
      if (currentWidth > max) {
        const newWidth = max;
        setCurrentWidth(newWidth);
        dispatch(actions.setPanelWidth(newWidth));
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [currentWidth, dispatch, getConstraints]);

  // Clean up event listeners on unmount
  useEffect(() => {
    return () => {
      if (mouseMoveRef.current) {
        document.removeEventListener('mousemove', mouseMoveRef.current);
      }
      if (mouseUpRef.current) {
        document.removeEventListener('mouseup', mouseUpRef.current);
      }
      if (touchMoveRef.current) {
        document.removeEventListener('touchmove', touchMoveRef.current);
      }
      if (touchEndRef.current) {
        document.removeEventListener('touchend', touchEndRef.current);
      }
      
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, []);

  return {
    currentWidth,
    isDragging,
    isMaximized,
    startDrag,
    startTouchDrag,
    constraints: getConstraints(),
  };
}
