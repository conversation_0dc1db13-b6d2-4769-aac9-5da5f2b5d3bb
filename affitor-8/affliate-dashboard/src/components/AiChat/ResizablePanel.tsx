"use client";

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actions, selectPanelWidth, selectIsMaximized } from '@/features/aiscript/aiscript.slice';

interface ResizablePanelProps {
  children: React.ReactNode;
  isExpanded: boolean;
  className?: string;
}

export default function ResizablePanel({ children, isExpanded, className = '' }: ResizablePanelProps) {
  const dispatch = useDispatch();
  const panelWidth = useSelector(selectPanelWidth);
  const isMaximized = useSelector(selectIsMaximized);
  
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartX, setDragStartX] = useState(0);
  const [dragStartWidth, setDragStartWidth] = useState(0);
  const [currentWidth, setCurrentWidth] = useState(panelWidth);
  const panelRef = useRef<HTMLDivElement>(null);

  // Update current width when Redux state changes
  useEffect(() => {
    setCurrentWidth(panelWidth);
  }, [panelWidth]);

  // Calculate constraints
  const getConstraints = useCallback(() => {
    const viewportWidth = typeof window !== 'undefined' ? window.innerWidth : 1200;
    const maxWidth = Math.min(800, viewportWidth * 0.45);
    const minWidth = 320;
    return { minWidth, maxWidth };
  }, []);

  // Handle mouse down on resize handle
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (isMaximized) return; // Don't allow resize when maximized
    
    e.preventDefault();
    setIsDragging(true);
    setDragStartX(e.clientX);
    setDragStartWidth(currentWidth);
    
    // Add global mouse event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = 'ew-resize';
    document.body.style.userSelect = 'none';
  }, [currentWidth, isMaximized]);

  // Handle mouse move during drag
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;
    
    const deltaX = dragStartX - e.clientX; // Negative because we're resizing from the left
    const newWidth = dragStartWidth + deltaX;
    const { minWidth, maxWidth } = getConstraints();
    
    // Apply constraints
    const constrainedWidth = Math.max(minWidth, Math.min(newWidth, maxWidth));
    setCurrentWidth(constrainedWidth);
  }, [isDragging, dragStartX, dragStartWidth, getConstraints]);

  // Handle mouse up to end drag
  const handleMouseUp = useCallback(() => {
    if (!isDragging) return;
    
    setIsDragging(false);
    
    // Update Redux state with final width
    dispatch(actions.setPanelWidth(currentWidth));
    
    // Clean up global event listeners
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, [isDragging, currentWidth, dispatch, handleMouseMove]);

  // Handle touch events for mobile
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (isMaximized) return;
    
    const touch = e.touches[0];
    setIsDragging(true);
    setDragStartX(touch.clientX);
    setDragStartWidth(currentWidth);
    
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);
  }, [currentWidth, isMaximized]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!isDragging) return;
    
    e.preventDefault();
    const touch = e.touches[0];
    const deltaX = dragStartX - touch.clientX;
    const newWidth = dragStartWidth + deltaX;
    const { minWidth, maxWidth } = getConstraints();
    
    const constrainedWidth = Math.max(minWidth, Math.min(newWidth, maxWidth));
    setCurrentWidth(constrainedWidth);
  }, [isDragging, dragStartX, dragStartWidth, getConstraints]);

  const handleTouchEnd = useCallback(() => {
    if (!isDragging) return;
    
    setIsDragging(false);
    dispatch(actions.setPanelWidth(currentWidth));
    
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
  }, [isDragging, currentWidth, dispatch, handleTouchMove]);

  // Handle window resize to adjust max width
  useEffect(() => {
    const handleResize = () => {
      const { maxWidth } = getConstraints();
      if (currentWidth > maxWidth) {
        const newWidth = maxWidth;
        setCurrentWidth(newWidth);
        dispatch(actions.setPanelWidth(newWidth));
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [currentWidth, dispatch, getConstraints]);

  // Clean up event listeners on unmount
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);

  // Calculate panel styles
  const panelStyles = isExpanded
    ? {
        position: 'fixed' as const,
        bottom: 0,
        right: 0,
        width: '100vw',
        height: '100vh',
        maxWidth: 'none',
        zIndex: 9999,
      }
    : {
        position: 'fixed' as const,
        bottom: '10px',
        right: '10px',
        width: `${currentWidth}px`,
        maxWidth: '95vw',
        maxHeight: '60vh',
        zIndex: 9998,
      };

  return (
    <div
      ref={panelRef}
      className={`shadow-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 transition-all duration-300 rounded-2xl flex flex-col overflow-hidden ${className}`}
      style={panelStyles}
    >
      {/* Resize handle - only show when not expanded and not maximized */}
      {!isExpanded && !isMaximized && (
        <div
          className={`absolute left-0 top-0 bottom-0 w-1 cursor-ew-resize hover:bg-blue-500 transition-colors ${
            isDragging ? 'bg-blue-500' : 'bg-transparent hover:bg-blue-300'
          }`}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
          style={{ zIndex: 10 }}
        >
          {/* Visual indicator */}
          <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-gray-300 dark:bg-gray-600 rounded-r opacity-50 hover:opacity-100 transition-opacity" />
        </div>
      )}

      {/* Width indicator during resize */}
      {isDragging && (
        <div className="absolute top-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded z-20">
          {Math.round(currentWidth)}px
        </div>
      )}

      {children}
    </div>
  );
}
