# remember on implement
- The UI should implement for both mobile and desktop
- Handle for dark mode too
- If need to use the token let get it from the localStorage (const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;)
- In case implement with the request to BE, remember to implement the proxy API
- In the request BE let use the client instead of create another fetch